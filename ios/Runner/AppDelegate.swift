import Firebase

import Flutter

import GoogleMaps

import UIKit

import UserNotifications

@UIApplicationMain
@objc class AppDelegate: FlutterAppDelegate {
  override func application(
    _ application: UIApplication,
    didFinishLaunchingWithOptions launchOptions: [UIApplication.LaunchOptionsKey: Any]?
  ) -> Bool {
    // Don't configure Firebase here - it will be done in Flutter main.dart
    // FirebaseApp.configure() - REMOVED to avoid double initialization

    GMSServices.provideAPIKey("AIzaSyDmpwqnRpySPiDVht0Rg6sPh1ZP3cBF8fc")
    GeneratedPluginRegistrant.register(with: self)

    // Enable Firebase Messaging for iOS
    if #available(iOS 10.0, *) {
      UNUserNotificationCenter.current().delegate = self
      let authOptions: UNAuthorizationOptions = [.alert, .badge, .sound]
      UNUserNotificationCenter.current().requestAuthorization(
        options: authOptions,
        completionHandler: { _, _ in })
    } else {
      let settings: UIUserNotificationSettings =
        UIUserNotificationSettings(types: [.alert, .badge, .sound], categories: nil)
      application.registerUserNotificationSettings(settings)
    }

    application.registerForRemoteNotifications()

    return super.application(application, didFinishLaunchingWithOptions: launchOptions)
  }
}
