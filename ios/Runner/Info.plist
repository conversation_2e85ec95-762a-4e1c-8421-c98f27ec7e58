<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>CFBundleDevelopmentRegion</key>
	<string>$(DEVELOPMENT_LANGUAGE)</string>
	<key>CFBundleDisplayName</key>
	<string>Busaty-Parents</string>
	<key>CFBundleExecutable</key>
	<string>$(EXECUTABLE_NAME)</string>
	<key>CFBundleIdentifier</key>
	<string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
	<key>CFBundleInfoDictionaryVersion</key>
	<string>6.0</string>
	<key>CFBundleName</key>
	<string>Busaty-Parents</string>
	<key>CFBundlePackageType</key>
	<string>APPL</string>
	<key>CFBundleShortVersionString</key>
	<string>1.0.2</string>
	<key>CFBundleSignature</key>
	<string>????</string>
	<key>CFBundleVersion</key>
	<string>1</string>
	<key>LSRequiresIPhoneOS</key>
	<true/>
	<key>UILaunchStoryboardName</key>
	<string>LaunchScreen</string>
	<key>UIMainStoryboardFile</key>
	<string>Main</string>
	<key>UISupportedInterfaceOrientations</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
		<string>UIInterfaceOrientationLandscapeLeft</string>
		<string>UIInterfaceOrientationLandscapeRight</string>
	</array>
	<key>UISupportedInterfaceOrientations~ipad</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
		<string>UIInterfaceOrientationPortraitUpsideDown</string>
		<string>UIInterfaceOrientationLandscapeLeft</string>
		<string>UIInterfaceOrientationLandscapeRight</string>
	</array>
	<key>CADisableMinimumFrameDurationOnPhone</key>
	<true/>
	<key>UIApplicationSupportsIndirectInputEvents</key>
	<true/>
	<key>CFBundleURLTypes</key>
	<array>
		<dict>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>busatyparents</string>
			</array>
			<key>CFBundleURLName</key>
			<string>com.busaty.parent</string>
		</dict>
	</array>
	<key>NSLocalNetworkUsageDescription</key>
	<string>Allow Flutter tools to connect to your device</string>
	<key>NSBonjourServices</key>
	<array>
		<string>_dartobservatory._tcp</string>
	</array>
	<key>GADApplicationIdentifier</key>
	<string>ca-app-pub-3940256099942544~1458002511</string>
	<key>SKAdNetworkItems</key>
	<array>
		<dict>
			<key>SKAdNetworkIdentifier</key>
			<string>cstr6suwn9.skadnetwork</string>
		</dict>
	</array>
	<key>NSLocalNotificationsUsageDescription</key>
	<string>We need your permission to show notifications</string>
	<key>UIBackgroundModes</key>
	<array>
		<string>fetch</string>
		<string>remote-notification</string>
	</array>
	<key>FirebaseAppDelegateProxyEnabled</key>
	<false/>
	<key>NSAppTransportSecurity</key>
	<dict>
		<key>NSAllowsArbitraryLoads</key>
		<true/>
		<key>NSAllowsArbitraryLoadsUsageDescription</key>
		<string>We need to access our secure server to provide real-time updates about your child&apos;s location, synchronize supervisor information, and ensure reliable communication between parents and school staff. This access is essential for maintaining the safety and security of the child handover process.</string>
		<key>NSAllowsArbitraryLoadsInWebContent</key>
		<true/>
		<key>NSAllowsLocalNetworking</key>
		<true/>
	</dict>
	<key>com.apple.developer.in-app-payments</key>
	<array>
		<string>merchant.com.busaty.parent</string>
	</array>
	<key>NSPhotoLibraryUsageDescription</key>
	<string>We need access to your photo library to let you select and share photos with other users in the app. For example, you can choose profile pictures or share images in chat conversations.</string>
	<key>NSLocationAlwaysAndWhenInUseUsageDescription</key>
	<string>We need your location to coordinate the safe handover of your child between you and the school supervisor. For example, we use your location to guide the supervisor to your exact pickup/dropoff point, send notifications when the supervisor is approaching, and confirm successful handover of your child.</string>
	<key>NSLocationWhenInUseUsageDescription</key>
	<string>We need your location to facilitate the safe handover of your child with the school supervisor. This allows us to show your exact location to the supervisor for pickup/dropoff, calculate accurate arrival times, and ensure smooth coordination during the handover process.</string>
</dict>
</plist>
