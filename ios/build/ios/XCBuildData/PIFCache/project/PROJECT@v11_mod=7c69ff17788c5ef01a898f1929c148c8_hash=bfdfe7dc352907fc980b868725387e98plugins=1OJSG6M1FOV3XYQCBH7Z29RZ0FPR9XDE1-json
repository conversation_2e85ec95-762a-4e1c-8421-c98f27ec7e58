{"appPreferencesBuildSettings": {}, "buildConfigurations": [{"buildSettings": {"ALWAYS_SEARCH_USER_PATHS": "NO", "CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED": "YES", "CLANG_ANALYZER_NONNULL": "YES", "CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION": "YES_AGGRESSIVE", "CLANG_CXX_LANGUAGE_STANDARD": "gnu++14", "CLANG_CXX_LIBRARY": "libc++", "CLANG_ENABLE_MODULES": "YES", "CLANG_ENABLE_OBJC_ARC": "YES", "CLANG_ENABLE_OBJC_WEAK": "YES", "CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING": "YES", "CLANG_WARN_BOOL_CONVERSION": "YES", "CLANG_WARN_COMMA": "YES", "CLANG_WARN_CONSTANT_CONVERSION": "YES", "CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS": "YES", "CLANG_WARN_DIRECT_OBJC_ISA_USAGE": "YES_ERROR", "CLANG_WARN_DOCUMENTATION_COMMENTS": "YES", "CLANG_WARN_EMPTY_BODY": "YES", "CLANG_WARN_ENUM_CONVERSION": "YES", "CLANG_WARN_INFINITE_RECURSION": "YES", "CLANG_WARN_INT_CONVERSION": "YES", "CLANG_WARN_NON_LITERAL_NULL_CONVERSION": "YES", "CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF": "YES", "CLANG_WARN_OBJC_LITERAL_CONVERSION": "YES", "CLANG_WARN_OBJC_ROOT_CLASS": "YES_ERROR", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "YES", "CLANG_WARN_RANGE_LOOP_ANALYSIS": "YES", "CLANG_WARN_STRICT_PROTOTYPES": "YES", "CLANG_WARN_SUSPICIOUS_MOVE": "YES", "CLANG_WARN_UNGUARDED_AVAILABILITY": "YES_AGGRESSIVE", "CLANG_WARN_UNREACHABLE_CODE": "YES", "CLANG_WARN__DUPLICATE_METHOD_MATCH": "YES", "COPY_PHASE_STRIP": "NO", "DEBUG_INFORMATION_FORMAT": "dwarf", "ENABLE_STRICT_OBJC_MSGSEND": "YES", "ENABLE_TESTABILITY": "YES", "GCC_C_LANGUAGE_STANDARD": "gnu11", "GCC_DYNAMIC_NO_PIC": "NO", "GCC_NO_COMMON_BLOCKS": "YES", "GCC_OPTIMIZATION_LEVEL": "0", "GCC_PREPROCESSOR_DEFINITIONS": "POD_CONFIGURATION_DEBUG=1 DEBUG=1 $(inherited)", "GCC_WARN_64_TO_32_BIT_CONVERSION": "YES", "GCC_WARN_ABOUT_RETURN_TYPE": "YES_ERROR", "GCC_WARN_UNDECLARED_SELECTOR": "YES", "GCC_WARN_UNINITIALIZED_AUTOS": "YES_AGGRESSIVE", "GCC_WARN_UNUSED_FUNCTION": "YES", "GCC_WARN_UNUSED_VARIABLE": "YES", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "MTL_ENABLE_DEBUG_INFO": "INCLUDE_SOURCE", "MTL_FAST_MATH": "YES", "ONLY_ACTIVE_ARCH": "YES", "PRODUCT_NAME": "$(TARGET_NAME)", "STRIP_INSTALLED_PRODUCT": "NO", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "DEBUG", "SWIFT_OPTIMIZATION_LEVEL": "-<PERSON><PERSON>", "SWIFT_VERSION": "5.0", "SYMROOT": "${SRCROOT}/../build"}, "guid": "bfdfe7dc352907fc980b868725387e98c329620c51892527db69ac984ef9321b", "name": "Debug"}, {"buildSettings": {"ALWAYS_SEARCH_USER_PATHS": "NO", "CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED": "YES", "CLANG_ANALYZER_NONNULL": "YES", "CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION": "YES_AGGRESSIVE", "CLANG_CXX_LANGUAGE_STANDARD": "gnu++14", "CLANG_CXX_LIBRARY": "libc++", "CLANG_ENABLE_MODULES": "YES", "CLANG_ENABLE_OBJC_ARC": "YES", "CLANG_ENABLE_OBJC_WEAK": "YES", "CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING": "YES", "CLANG_WARN_BOOL_CONVERSION": "YES", "CLANG_WARN_COMMA": "YES", "CLANG_WARN_CONSTANT_CONVERSION": "YES", "CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS": "YES", "CLANG_WARN_DIRECT_OBJC_ISA_USAGE": "YES_ERROR", "CLANG_WARN_DOCUMENTATION_COMMENTS": "YES", "CLANG_WARN_EMPTY_BODY": "YES", "CLANG_WARN_ENUM_CONVERSION": "YES", "CLANG_WARN_INFINITE_RECURSION": "YES", "CLANG_WARN_INT_CONVERSION": "YES", "CLANG_WARN_NON_LITERAL_NULL_CONVERSION": "YES", "CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF": "YES", "CLANG_WARN_OBJC_LITERAL_CONVERSION": "YES", "CLANG_WARN_OBJC_ROOT_CLASS": "YES_ERROR", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "YES", "CLANG_WARN_RANGE_LOOP_ANALYSIS": "YES", "CLANG_WARN_STRICT_PROTOTYPES": "YES", "CLANG_WARN_SUSPICIOUS_MOVE": "YES", "CLANG_WARN_UNGUARDED_AVAILABILITY": "YES_AGGRESSIVE", "CLANG_WARN_UNREACHABLE_CODE": "YES", "CLANG_WARN__DUPLICATE_METHOD_MATCH": "YES", "COPY_PHASE_STRIP": "NO", "DEBUG_INFORMATION_FORMAT": "dwarf-with-dsym", "ENABLE_NS_ASSERTIONS": "NO", "ENABLE_STRICT_OBJC_MSGSEND": "YES", "GCC_C_LANGUAGE_STANDARD": "gnu11", "GCC_NO_COMMON_BLOCKS": "YES", "GCC_PREPROCESSOR_DEFINITIONS": "POD_CONFIGURATION_PROFILE=1 $(inherited)", "GCC_WARN_64_TO_32_BIT_CONVERSION": "YES", "GCC_WARN_ABOUT_RETURN_TYPE": "YES_ERROR", "GCC_WARN_UNDECLARED_SELECTOR": "YES", "GCC_WARN_UNINITIALIZED_AUTOS": "YES_AGGRESSIVE", "GCC_WARN_UNUSED_FUNCTION": "YES", "GCC_WARN_UNUSED_VARIABLE": "YES", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "MTL_ENABLE_DEBUG_INFO": "NO", "MTL_FAST_MATH": "YES", "PRODUCT_NAME": "$(TARGET_NAME)", "STRIP_INSTALLED_PRODUCT": "NO", "SWIFT_COMPILATION_MODE": "wholemodule", "SWIFT_OPTIMIZATION_LEVEL": "-O", "SWIFT_VERSION": "5.0", "SYMROOT": "${SRCROOT}/../build"}, "guid": "bfdfe7dc352907fc980b868725387e986eaba3bbf34fffc52894406988f981b0", "name": "Profile"}, {"buildSettings": {"ALWAYS_SEARCH_USER_PATHS": "NO", "CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED": "YES", "CLANG_ANALYZER_NONNULL": "YES", "CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION": "YES_AGGRESSIVE", "CLANG_CXX_LANGUAGE_STANDARD": "gnu++14", "CLANG_CXX_LIBRARY": "libc++", "CLANG_ENABLE_MODULES": "YES", "CLANG_ENABLE_OBJC_ARC": "YES", "CLANG_ENABLE_OBJC_WEAK": "YES", "CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING": "YES", "CLANG_WARN_BOOL_CONVERSION": "YES", "CLANG_WARN_COMMA": "YES", "CLANG_WARN_CONSTANT_CONVERSION": "YES", "CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS": "YES", "CLANG_WARN_DIRECT_OBJC_ISA_USAGE": "YES_ERROR", "CLANG_WARN_DOCUMENTATION_COMMENTS": "YES", "CLANG_WARN_EMPTY_BODY": "YES", "CLANG_WARN_ENUM_CONVERSION": "YES", "CLANG_WARN_INFINITE_RECURSION": "YES", "CLANG_WARN_INT_CONVERSION": "YES", "CLANG_WARN_NON_LITERAL_NULL_CONVERSION": "YES", "CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF": "YES", "CLANG_WARN_OBJC_LITERAL_CONVERSION": "YES", "CLANG_WARN_OBJC_ROOT_CLASS": "YES_ERROR", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "YES", "CLANG_WARN_RANGE_LOOP_ANALYSIS": "YES", "CLANG_WARN_STRICT_PROTOTYPES": "YES", "CLANG_WARN_SUSPICIOUS_MOVE": "YES", "CLANG_WARN_UNGUARDED_AVAILABILITY": "YES_AGGRESSIVE", "CLANG_WARN_UNREACHABLE_CODE": "YES", "CLANG_WARN__DUPLICATE_METHOD_MATCH": "YES", "COPY_PHASE_STRIP": "NO", "DEBUG_INFORMATION_FORMAT": "dwarf-with-dsym", "ENABLE_NS_ASSERTIONS": "NO", "ENABLE_STRICT_OBJC_MSGSEND": "YES", "GCC_C_LANGUAGE_STANDARD": "gnu11", "GCC_NO_COMMON_BLOCKS": "YES", "GCC_PREPROCESSOR_DEFINITIONS": "POD_CONFIGURATION_RELEASE=1 $(inherited)", "GCC_WARN_64_TO_32_BIT_CONVERSION": "YES", "GCC_WARN_ABOUT_RETURN_TYPE": "YES_ERROR", "GCC_WARN_UNDECLARED_SELECTOR": "YES", "GCC_WARN_UNINITIALIZED_AUTOS": "YES_AGGRESSIVE", "GCC_WARN_UNUSED_FUNCTION": "YES", "GCC_WARN_UNUSED_VARIABLE": "YES", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "MTL_ENABLE_DEBUG_INFO": "NO", "MTL_FAST_MATH": "YES", "PRODUCT_NAME": "$(TARGET_NAME)", "STRIP_INSTALLED_PRODUCT": "NO", "SWIFT_COMPILATION_MODE": "wholemodule", "SWIFT_OPTIMIZATION_LEVEL": "-O", "SWIFT_VERSION": "5.0", "SYMROOT": "${SRCROOT}/../build"}, "guid": "bfdfe7dc352907fc980b868725387e9804db47a3ceef83edd118018eb43bf272", "name": "Release"}], "classPrefix": "", "defaultConfigurationName": "Release", "developmentRegion": "en", "groupTree": {"children": [{"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e98d0b25d39b515a574839e998df229c3cb", "path": "../Podfile", "sourceTree": "SOURCE_ROOT", "type": "file"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98e68ddf4b77f4f45e620137d5eeb00d2f", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/app_links-6.3.3/ios/Classes/AppLinksPlugin.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98eca94910330641fbfad1836dc244c857", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/app_links-6.3.3/ios/Classes/AppLinksPlugin.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e983d0fc46ebd6802aff2fdb3c7d55b1e28", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/app_links-6.3.3/ios/Classes/SwiftAppLinksPlugin.swift", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98f3c81e4a5688899935180fe5be5d4955", "name": "Classes", "path": "Classes", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e98d783f6fbba81e2a5cdd81e37490d26e9", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/app_links-6.3.3/ios/Resources/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e986642e19a19e485d5212f420204d7ad6f", "name": "Resources", "path": "Resources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9885590ab53def3ec4c7915a235406e61e", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98e1d8ecf06e30e519ee56daa7b04169d8", "name": "app_links", "path": "app_links", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98e653f7262dd870367f53591a57747f0e", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e989a24c65cb3817732fa85cbe18fc758bf", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98e4a570351aa5b33660d5095aae256782", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98a738e2b2667518dfdec927169523ee57", "name": "test_notifictions", "path": "test_notifictions", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98b69f32257f2b613e3a6c1ee3f952b651", "name": "StudioProjects", "path": "StudioProjects", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e980b116f5001a240786dc49bc3faa2107f", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98aaa25b9f56e0f4c9ebfc8bd051cf77db", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98a08d4e58157514d6caa6a26347ae6536", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98ab51c7a6de6e7674b5275c494979b526", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98499686f3892c14edb4edabfee5825649", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98803e5dfeb6cf4ad77f737cf78937d0fc", "name": "..", "path": "../../../../../../../.pub-cache/hosted/pub.dev/app_links-6.3.3/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e98961eb0c8c675224ef944e4adb5d930ae", "path": "../../../../../../../.pub-cache/hosted/pub.dev/app_links-6.3.3/ios/app_links.podspec", "sourceTree": "<group>", "type": "file"}, {"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e9867659e70bee4982509ad0d4c1de3d342", "path": "../../../../../../../.pub-cache/hosted/pub.dev/app_links-6.3.3/LICENSE", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98751dd2114afd9946b28a59e09f7c7333", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e98b7bed279bc22ac15d2bd7e79439ce228", "path": "app_links.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e989c1ac04516db8e062a565c16ee7405ef", "path": "app_links-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98c7b12f10bb352c9e56046835a4585655", "path": "app_links-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9815c989345430380250baa5569e822f10", "path": "app_links-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e985d76fd4102700b58d8e2a2577cfa097f", "path": "app_links-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98185d87ca5674786bf97c08b04d7b4763", "path": "app_links.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e9878cc2ac636bdd425f1679e9d4d2cf44b", "path": "app_links.release.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e986a5d9bbc2a81ba439294095cd9464764", "path": "ResourceBundle-app_links_ios_privacy-app_links-Info.plist", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9819ea6432862cd959e7ff2bcfdfea88c1", "name": "Support Files", "path": "../../../../Pods/Target Support Files/app_links", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e980bb4384409d3be0eada812e272d450c5", "name": "app_links", "path": "../.symlinks/plugins/app_links/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e983f4efd3444a35778298f932b4232f167", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/audioplayers_darwin-6.0.0/ios/Classes/AudioContext.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98769faf3b0cdc24f6b607cf605a7b22c7", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/audioplayers_darwin-6.0.0/ios/Classes/AudioplayersDarwinPlugin.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98a4f356ed9c90f68c1fc3de6bcb78f687", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/audioplayers_darwin-6.0.0/ios/Classes/AudioplayersDarwinPlugin.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e989d6624620d5b3b79456df880c0c45447", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/audioplayers_darwin-6.0.0/ios/Classes/SwiftAudioplayersDarwinPlugin.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98f260666bdc4e29567338930dfa358516", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/audioplayers_darwin-6.0.0/ios/Classes/Utils.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98b9896d60e946799b44ed0f6e480c83df", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/audioplayers_darwin-6.0.0/ios/Classes/WrappedMediaPlayer.swift", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98f2e237a39dd7c330bfe8b14fd5783c44", "name": "Classes", "path": "Classes", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98e7275a3ffaec2c2b7d4bb4ce656d0412", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9839b01b09cf63fd9ae2e3c1bd861eaf42", "name": "audioplayers_darwin", "path": "audioplayers_darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98e277b4f1a67fea74a9e9e322d476aa61", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98687dd0c26fbf8e399df6a0174ab63a41", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e986172c69ad743b85c76af1a43c98d9a35", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e985715c6e9418793b52a11e25a8855aa26", "name": "test_notifictions", "path": "test_notifictions", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e984bbffae53f5ae62ea0ffad378609c585", "name": "StudioProjects", "path": "StudioProjects", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98f8f62cf55bb8b3c0e8ba91ad011fe4c5", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e987f071e9c7a8d9ea2cbd4fe875e9df8c8", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98f17ee7ebf58db459079ba06979ed79fd", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e986b52a32107730971d1bb36f2a4816bf3", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98cbda7c87ea4c50805fa5b4a95c01abb7", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98b0c500204ab6314bba07dfdefc869cbc", "name": "..", "path": "../../../../../../../.pub-cache/hosted/pub.dev/audioplayers_darwin-6.0.0/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e98f950c1d8a6eba0052bac6fbdc8b0aed7", "path": "../../../../../../../.pub-cache/hosted/pub.dev/audioplayers_darwin-6.0.0/ios/audioplayers_darwin.podspec", "sourceTree": "<group>", "type": "file"}, {"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e98852b00bdd2091db43ff97ee6b43c21b9", "path": "../../../../../../../.pub-cache/hosted/pub.dev/audioplayers_darwin-6.0.0/LICENSE", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98362941eb714d39f7e8f4a2d02e4f0d69", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e983f7e99a41a2f50814c10032b771b90ff", "path": "audioplayers_darwin.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e985b00146a9441918909ae0a8aeafcce8e", "path": "audioplayers_darwin-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98f99e600d8d58d74bbbdb385c4ad4c8e5", "path": "audioplayers_darwin-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9868648189372297202833f9818a3731c5", "path": "audioplayers_darwin-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98ad1574500f87762bcd1749cacdaaa346", "path": "audioplayers_darwin-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98f0df8e5643cb39cd3c68c05832f82e1c", "path": "audioplayers_darwin.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e984bbbb01fd7ac274cdcdc6d6dc3096d41", "path": "audioplayers_darwin.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e984738c4d6f370d3e8c59ff6737416b72f", "name": "Support Files", "path": "../../../../Pods/Target Support Files/audioplayers_darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9828a5d85bb4c216984163638c799e51bc", "name": "audioplayers_darwin", "path": "../.symlinks/plugins/audioplayers_darwin/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98e1850dceb1e141be66ffaaa6f9db4cc9", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/connectivity_plus-6.1.1/darwin/connectivity_plus/Sources/connectivity_plus/ConnectivityPlusPlugin.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e984be58e39a9e492967c5a828d566c5f23", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/connectivity_plus-6.1.1/darwin/connectivity_plus/Sources/connectivity_plus/ConnectivityProvider.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9879f78e13011891a8567952692bfb0468", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/connectivity_plus-6.1.1/darwin/connectivity_plus/Sources/connectivity_plus/PathMonitorConnectivityProvider.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e98477db9fded62eadfae42b9bd6ff182d8", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/connectivity_plus-6.1.1/darwin/connectivity_plus/Sources/connectivity_plus/PrivacyInfo-ios.xcprivacy", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e988f6c8a6d9b81441db6b2afdd78db69dc", "name": "connectivity_plus", "path": "connectivity_plus", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98d8177fac1db160105d5e38e6f5bc95e7", "name": "Sources", "path": "Sources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e981bb5b49855f3b082a0e5d34e58be4735", "name": "connectivity_plus", "path": "connectivity_plus", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98203168a9976db6c889550b501964db0b", "name": "darwin", "path": "darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e980e695fe2a286c1ca1bcd3dc8ff665173", "name": "connectivity_plus", "path": "connectivity_plus", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e985c74e08733830db456776e6434361f53", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98a2f68cc6fb16fc5dac0d8a581c38af82", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e988740d703b3da1aaa973b88bcd63d45dd", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98776111c730dd0fb5c148167210b978fd", "name": "test_notifictions", "path": "test_notifictions", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e986ace29ee984e262664f3daae01bc6110", "name": "StudioProjects", "path": "StudioProjects", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9864988500a03027dbf20a115b462b37ea", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98dc9c90ffca94b57d3c742f245d2c953b", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9819dff577e810924fbd8367ba8ec9fe16", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98277a7bbedbba1b184dc7a5d70375d5ac", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98d441b7b3d6ef653d88b7036af54e4156", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e989adb18158e83fcdc22d308ceffb3661a", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e987b1c8bc40c17d5679df6d3fead810ec1", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98be6e2a330fe7ebbb4fbd55c8c5405d6f", "name": "..", "path": "../../../../../../../.pub-cache/hosted/pub.dev/connectivity_plus-6.1.1/darwin/connectivity_plus/Sources", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e98d4521632d2eb69da4c217296b6cbfb71", "path": "../../../../../../../.pub-cache/hosted/pub.dev/connectivity_plus-6.1.1/darwin/connectivity_plus.podspec", "sourceTree": "<group>", "type": "file"}, {"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e981a91be67fa7bd94ab81f09f4ca141a24", "path": "../../../../../../../.pub-cache/hosted/pub.dev/connectivity_plus-6.1.1/LICENSE", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98b2e4a6bbf5f3bba7245a53cb6c066bae", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e98660bf38f063fea9cfceeaf9026a94d43", "path": "connectivity_plus.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9862cb24cd99e0d6636ce349b45f96acbc", "path": "connectivity_plus-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e9896712577984d6964275b8ad94749cc04", "path": "connectivity_plus-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e985931fe837515ca182f48266b386cd61a", "path": "connectivity_plus-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e983489964837da5db282adc228a57f4158", "path": "connectivity_plus-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e9897ad953b80c481359d956d8af72dc833", "path": "connectivity_plus.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e983b8bfd0bf2f83ae28b7dcaab41b1faf3", "path": "connectivity_plus.release.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98ccec86eaeaf9bd4b15082381965f2395", "path": "ResourceBundle-connectivity_plus_privacy-connectivity_plus-Info.plist", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98acc6ab8a563eb4238fabb65001c3fd51", "name": "Support Files", "path": "../../../../Pods/Target Support Files/connectivity_plus", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e983150db2d193f0be28bef5e7b3fdc3285", "name": "connectivity_plus", "path": "../.symlinks/plugins/connectivity_plus/darwin", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e989e5ec4ad55ae8606fa7685c52f5cbbd8", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/file_picker-8.1.7/ios/Classes/FileInfo.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98c2cb3393e2816ced5b568fa197f18a13", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/file_picker-8.1.7/ios/Classes/FileInfo.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98ee2a72124b18087ebea3da0fb81fbcf6", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/file_picker-8.1.7/ios/Classes/FilePickerPlugin.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e984c7c2d83dcfb3364bb5f12bb0d9c98f1", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/file_picker-8.1.7/ios/Classes/FilePickerPlugin.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98eba328c94f6439ceccaa177e200b9691", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/file_picker-8.1.7/ios/Classes/FileUtils.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98b53cd2fa80e46753212e8a0a734ba88d", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/file_picker-8.1.7/ios/Classes/FileUtils.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e984da2fbdc6c6b6bffbc23a0784cfa5270", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/file_picker-8.1.7/ios/Classes/ImageUtils.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98645dae66a7f09486277622b64b75e119", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/file_picker-8.1.7/ios/Classes/ImageUtils.m", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9883c6513fa4825056522fa339682ee060", "name": "Classes", "path": "Classes", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e986b8bf0e4deead1402078af318cdd14d4", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/file_picker-8.1.7/ios/Resources/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98f9eb03d409b700758c2bf1009870c522", "name": "Resources", "path": "Resources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98638375d237b8df0f3727e9f920f394c6", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98a18397b7a4f2c1d8105a19368455f569", "name": "file_picker", "path": "file_picker", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e985b178cb4b29582c8fbe4a532ff3c9d6d", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98b2810a601690c36474b4ae004ceaaaea", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98c3c473b4eb6a7303c1659b208c34987c", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e989696ae60cd5b170830d4f2617812a1ae", "name": "test_notifictions", "path": "test_notifictions", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e984f8890b169f841941fac68cd247b8429", "name": "StudioProjects", "path": "StudioProjects", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e986bee9789d3407ce9a58e94412b20470b", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98d30c051650f1a2e059aafbd46fdddd89", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98886bff9a74e24ba6460bdafc2bf52314", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98ffa3f6d860749e6745425714c2d495b8", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98a9479e6096eb96dcdada2908a1d25b36", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9880c53ea0f7aa3c3f3f7612c8a5c33443", "name": "..", "path": "../../../../../../../.pub-cache/hosted/pub.dev/file_picker-8.1.7/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e980c1c9838badd444d6a3e7b5954b26548", "path": "../../../../../../../.pub-cache/hosted/pub.dev/file_picker-8.1.7/ios/file_picker.podspec", "sourceTree": "<group>", "type": "file"}, {"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e98f9389f3b1b4b7cbafa830c6da6bbbe26", "path": "../../../../../../../.pub-cache/hosted/pub.dev/file_picker-8.1.7/LICENSE", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98db4a694a789cf8bef62c54426258e644", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e98b81f27857b6f3db7da6c3861689c46f8", "path": "file_picker.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98a9e7f2d92d0e8f81c38a13312e871c9b", "path": "file_picker-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e986e0bcfa6e336970772d5ff7f8e42a282", "path": "file_picker-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e986d994cc518ddce696f69ea643a8ccb6a", "path": "file_picker-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e988f8e91e99207d21d84d41391b330aacb", "path": "file_picker-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e985243168001e586a405812f946c7127be", "path": "file_picker.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98f8f8d7d059aecde9951e1017b771f8f5", "path": "file_picker.release.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98ae580cb38ab8d9d47f6d912599cf5461", "path": "ResourceBundle-file_picker_ios_privacy-file_picker-Info.plist", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e980a2334809b220be78bcea5322753f524", "name": "Support Files", "path": "../../../../Pods/Target Support Files/file_picker", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9846e3dd55744b47c6e63a46f9470e9097", "name": "file_picker", "path": "../.symlinks/plugins/file_picker/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98fd17a80a4947d0a58de98164759ca6b4", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/firebase_core-3.10.0/ios/firebase_core/Sources/firebase_core/dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9840e436b3bcd9622a1a47d9d5d2ec13fa", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/firebase_core-3.10.0/ios/firebase_core/Sources/firebase_core/FLTFirebaseCorePlugin.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e980c676c22ee19f983d76f936c308f3db5", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/firebase_core-3.10.0/ios/firebase_core/Sources/firebase_core/FLTFirebasePlugin.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98821dee015d285bcbcaa290409278e449", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/firebase_core-3.10.0/ios/firebase_core/Sources/firebase_core/FLTFirebasePluginRegistry.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9846bcb5cdba02a9be8fbdb281205be3fe", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/firebase_core-3.10.0/ios/firebase_core/Sources/firebase_core/messages.g.m", "sourceTree": "<group>", "type": "file"}, {"children": [{"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e982a72190dc3f550f5bef3c373b92d880d", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/firebase_core-3.10.0/ios/firebase_core/Sources/firebase_core/include/firebase_core/dummy.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9894e13365b931bc20a51b92b6ae33f07d", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/firebase_core-3.10.0/ios/firebase_core/Sources/firebase_core/include/firebase_core/FLTFirebaseCorePlugin.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9811200a0844b2310c92f5338bd863695c", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/firebase_core-3.10.0/ios/firebase_core/Sources/firebase_core/include/firebase_core/FLTFirebasePlugin.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98970fcfa03c27703f148d9c3fa4c0ecb7", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/firebase_core-3.10.0/ios/firebase_core/Sources/firebase_core/include/firebase_core/FLTFirebasePluginRegistry.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98ec504071351ebb889660fe6c16907751", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/firebase_core-3.10.0/ios/firebase_core/Sources/firebase_core/include/firebase_core/messages.g.h", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e981a0a7b606d31c1b7c2f4221b1b0df554", "name": "firebase_core", "path": "firebase_core", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e981e2e731c7994edc97eddbf7fc67ef439", "name": "include", "path": "include", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e986fe42c40cc6fc97c5b9305d00272fb13", "name": "firebase_core", "path": "firebase_core", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98cf805b826bdbf8caf069d5697d94ee55", "name": "Sources", "path": "Sources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98050ae4eeed67285c1811d9b8cd561f8e", "name": "firebase_core", "path": "firebase_core", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9892b01bce7582e5b07ee1a295a0539310", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e986de0df0666631006e7f5aec4460fd080", "name": "firebase_core", "path": "firebase_core", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98bfe30b2114fdee114c15dd0332e8f9ce", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98031899e76fa629b26a3b0a8ff783e062", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e988d5acf58895d1fe3f1ac2de6c882fbd1", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98fcbe29c6e2b1ccf2dc5e0e562eb0001e", "name": "test_notifictions", "path": "test_notifictions", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9836698309793e10431f9b439c993569a8", "name": "StudioProjects", "path": "StudioProjects", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e986a082a58d4bac238674397322c4093b3", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e987c79a53cb588c4a44bc2097de4ae980a", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e986df18677187909d3b319b6b9f8b406f0", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98d953a543054765b8b625df6d820b66e7", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e986a7cc471285753c6e82d17840fcdf413", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e987aa06867a5e4160cc577619785cbf5ed", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98765ffb324c2eec5df8771db9858d05fb", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e985f7074c35f6dedd44a6b8ccacd4ac61e", "name": "..", "path": "../../../../../../../.pub-cache/hosted/pub.dev/firebase_core-3.10.0/ios/firebase_core/Sources", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e98485e3bc83e4a048d9584c1904ba4677a", "path": "../../../../../../../.pub-cache/hosted/pub.dev/firebase_core-3.10.0/ios/firebase_core.podspec", "sourceTree": "<group>", "type": "file"}, {"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e98a8a7b07d55cf7565d91e504d11554474", "path": "../../../../../../../.pub-cache/hosted/pub.dev/firebase_core-3.10.0/LICENSE", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e987168ceab303d399ef47cddd2753b3a48", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e98160bcab88d22ad2b7b7e603a516fd58b", "path": "firebase_core.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e989726ac51bfdfaf23282858354dc808ed", "path": "firebase_core-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98a1ff756281ba63bb827186cfe0bfdbbe", "path": "firebase_core-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e989cdad978f8a7858d3ecda0b086e1513a", "path": "firebase_core-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98cd5eb9b7d2e37605fa64de430c2039dd", "path": "firebase_core-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98f34b0b28455a2cf43fe61cc0f2b49938", "path": "firebase_core.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e989c830655b368b0c2dd9e57188e1fde24", "path": "firebase_core.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98bb77c196d501c80ad5c4f928ca74acd6", "name": "Support Files", "path": "../../../../Pods/Target Support Files/firebase_core", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e985020f908bf42ba945941651a6e1838a0", "name": "firebase_core", "path": "../.symlinks/plugins/firebase_core/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98b08659a4c0f8747759dee937b2568721", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/firebase_messaging-15.1.6/ios/Classes/FLTFirebaseMessagingPlugin.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98496bbbe92e48f32aacb6edb722a81870", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/firebase_messaging-15.1.6/ios/Classes/FLTFirebaseMessagingPlugin.m", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9843befd9049a61ab5c977a99f9bbe1c9c", "name": "Classes", "path": "Classes", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e983287c92b796bfa8ccd95e7d21d80f0e4", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/firebase_messaging-15.1.6/ios/Resources/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98b82be37e4a0da659ceb84e6be29cbb51", "name": "Resources", "path": "Resources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98329fc470a755ea0c349367982b1fe04d", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98716a329bf4d35f7f5c1b27eedf1018cd", "name": "firebase_messaging", "path": "firebase_messaging", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9816902e4c5a545901cfdcb5ea3ed1c6d1", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e987588ac3bc7d515b599d809335e1a45f8", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98632c2c1edb6df3f21bc562ba278f8e38", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98e1fd9c3c0e080746b74dea0ef48ca69c", "name": "test_notifictions", "path": "test_notifictions", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9826f6e944206b86368b60d8cc4c5bd417", "name": "StudioProjects", "path": "StudioProjects", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e984f2f2394ecd1920f69fba37af460fdd1", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98cbe1b2dcf3f7d754c61dd195a1f2fd87", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98496e110a38c49b21315b5d7c687b7b6f", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98443264f41bd0e3d3c2883ae6aeeb39af", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98e725b0b99e11c221d4215214d5e2ed2a", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98c0ff2663534ad5e704b76d1554660a77", "name": "..", "path": "../../../../../../../.pub-cache/hosted/pub.dev/firebase_messaging-15.1.6/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e98291fb31ee710d0bfafe0ff2e072099e1", "path": "../../../../../../../.pub-cache/hosted/pub.dev/firebase_messaging-15.1.6/ios/firebase_messaging.podspec", "sourceTree": "<group>", "type": "file"}, {"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e9800ce293798a62093ec59f0abc2a6b6d7", "path": "../../../../../../../.pub-cache/hosted/pub.dev/firebase_messaging-15.1.6/LICENSE", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e981f6dc58d17a8430d21e0dc3dfc63f222", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e98ab8160037d39fd82cf5d6ded35fbc6b6", "path": "firebase_messaging.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e988be10cfdfd01ecb9fc237dbe0e864a6c", "path": "firebase_messaging-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e989f9e23ea977d874123741d242121fb88", "path": "firebase_messaging-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e986a7c6e61bc76557ba434ebb81bbfac92", "path": "firebase_messaging-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e989da72604f92385a70aa146543901c704", "path": "firebase_messaging-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e980466127d719e55b25b2cd50c1793c788", "path": "firebase_messaging.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98e0d139556a5ee9a442f3ba4091b6d17a", "path": "firebase_messaging.release.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e989048128aa53faa6f812c28a1c4b8d6f4", "path": "ResourceBundle-firebase_messaging_Privacy-firebase_messaging-Info.plist", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98c7135c4d2e6233ec5e074acdd3f68696", "name": "Support Files", "path": "../../../../Pods/Target Support Files/firebase_messaging", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98d06f64734f912ee0e3d2827d96911198", "name": "firebase_messaging", "path": "../.symlinks/plugins/firebase_messaging/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e9800fd6d11de4345ad27c7071dc493066e", "path": "Flutter.podspec", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98ace88775b653001774d5b0e1551b7ba0", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e9842fa6e068ddb82af9b50a17079c6608f", "path": "Flutter.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98117bd341328d1be09399e71227974b89", "path": "Flutter.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98860cac2090761389c8f14624bf66efcb", "name": "Support Files", "path": "../Pods/Target Support Files/Flutter", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9865de46d0733447bd569694c73204593e", "name": "Flutter", "path": "../Flutter", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9892f2d3f3ad4f07f0052b65b9c2618a70", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/flutter_inapp_purchase-5.6.1/ios/Classes/FlutterInappPurchasePlugin.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e984eb3ad75cca1d957f3bf0db9fb579846", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/flutter_inapp_purchase-5.6.1/ios/Classes/FlutterInappPurchasePlugin.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98ce1838262391af957ce88c65d408d517", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/flutter_inapp_purchase-5.6.1/ios/Classes/IAPPromotionObserver.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98da77ef33cf6ead1a63e0274adbadc873", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/flutter_inapp_purchase-5.6.1/ios/Classes/IAPPromotionObserver.m", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e989f8c315a9b0e4cbe59262eac8ee00d6f", "name": "Classes", "path": "Classes", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9802016b806aae8d5bcdebbfc338dd3754", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9845a6fa2e54224de45d548ece9f6ab23d", "name": "flutter_inapp_purchase", "path": "flutter_inapp_purchase", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9870c5380db59888cf898dad8202d392b1", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9857c3742483dccd1d41b7d05eebf76d53", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e983a251505c68cecb733fe5ef71410f83a", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e987120f388f356e4a776661aa1950445fd", "name": "test_notifictions", "path": "test_notifictions", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98d797ca84c695f54708d9f9d6bc6be898", "name": "StudioProjects", "path": "StudioProjects", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98c021de61fe6f79e24202575eda59b5fd", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98cb29f8e69aaa73f356d246b13527c343", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e983a72705f714531361f38ee41ad923396", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e980e02b40df719747c42641ceaede99e4d", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9857e370672a447e9a7de538933ba7a629", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98cc9b24cc67771e84af9f076a5ccd246b", "name": "..", "path": "../../../../../../../.pub-cache/hosted/pub.dev/flutter_inapp_purchase-5.6.1/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e982d20ae900f751fdfc638aaf911e7d7da", "path": "../../../../../../../.pub-cache/hosted/pub.dev/flutter_inapp_purchase-5.6.1/ios/flutter_inapp_purchase.podspec", "sourceTree": "<group>", "type": "file"}, {"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e98e9287830188dff9e2b06f7012a83c2ea", "path": "../../../../../../../.pub-cache/hosted/pub.dev/flutter_inapp_purchase-5.6.1/LICENSE", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98163510ed0ac09fe2b3181af3da7ca260", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e98a5a78932b58d27134a9ee2b37b4f355a", "path": "flutter_inapp_purchase.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98438daa972fea1676c50d3f36683bc564", "path": "flutter_inapp_purchase-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98fb19dc1f2e2c862199b74ef8e0cdd83f", "path": "flutter_inapp_purchase-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98744f68a61c4132db0f55b187978d2fdd", "path": "flutter_inapp_purchase-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98d36a166575b91d4b80ee0c122b78db8c", "path": "flutter_inapp_purchase-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e985a954596824bae3a7b7843545b874dcb", "path": "flutter_inapp_purchase.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98049ac1b009684ff92ba40ce20de63a0f", "path": "flutter_inapp_purchase.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98375c551e7fc661713d89c7dd90242642", "name": "Support Files", "path": "../../../../Pods/Target Support Files/flutter_inapp_purchase", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98879bcb2c9d42d9fcadd42559967ca9ce", "name": "flutter_inapp_purchase", "path": "../.symlinks/plugins/flutter_inapp_purchase/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98fb69bbffd6bf1187a040fd31c28f1ac1", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/flutter_isolate-2.1.0/ios/Classes/FlutterIsolatePlugin.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e982eb5d86c88aa43350f4a50215abcf0f1", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/flutter_isolate-2.1.0/ios/Classes/FlutterIsolatePlugin.m", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98f76180b0bb7aba829b2ec64be26d0266", "name": "Classes", "path": "Classes", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98afa1b6d6e31cb1d717f352930014d2d9", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98e0c5fb90a8aa3f561c017529fb4bdf0f", "name": "flutter_isolate", "path": "flutter_isolate", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e987a2eed65e8abe0d27ed56c73403bfc19", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98812ae358570a8f5600578edd9c686878", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e980fc7f6f0f281160dc46070201cdb8d75", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98abd226d6cf45c3cd1b10a5ef2b47dc44", "name": "test_notifictions", "path": "test_notifictions", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e988f0932528d5ed9cb6d2dddc86ac7f802", "name": "StudioProjects", "path": "StudioProjects", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98668dc9db2b3c83ffd8e3aa2edca6b4d4", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98e4008e5e38fc5d3ec4cc3f54a945b12a", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e987d379bfd209c0a4849117eb07d3e5e6a", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e988310693b64facd75c98ae7aa92dae302", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98dc3d8100c42e8fe4fd1fd6534c9c6b1c", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9848e7bccaab556331525e9f53bec40b2e", "name": "..", "path": "../../../../../../../.pub-cache/hosted/pub.dev/flutter_isolate-2.1.0/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e9896b3e276e5e61c26813dba4c00a44892", "path": "../../../../../../../.pub-cache/hosted/pub.dev/flutter_isolate-2.1.0/ios/flutter_isolate.podspec", "sourceTree": "<group>", "type": "file"}, {"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e98d56cf61e2d62b56fd4c4116d6b03c8ce", "path": "../../../../../../../.pub-cache/hosted/pub.dev/flutter_isolate-2.1.0/LICENSE", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e984f34f8eb99e6d52ad63678b14beaf8df", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e98dc708fd775f1debedb5bbdef8a7216c7", "path": "flutter_isolate.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98e18cb1fb1997782a96a11f023100a296", "path": "flutter_isolate-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98de5ab1fbeede142d21cc428d917d1d1b", "path": "flutter_isolate-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98265302b8c1d1eb9b66189cce53a3d4f7", "path": "flutter_isolate-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98873d9771aca84559e9e5ec31e453f848", "path": "flutter_isolate-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98ec957cf5ff5bfa1843d030ffd03ca1e5", "path": "flutter_isolate.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e9822e17cd83f25212410c0628694d6eb43", "path": "flutter_isolate.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e987cac6feefdef8739c9b742f42b8fd18b", "name": "Support Files", "path": "../../../../Pods/Target Support Files/flutter_isolate", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9833012d4b7ba01d5210e9c6dbd180c029", "name": "flutter_isolate", "path": "../.symlinks/plugins/flutter_isolate/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e982b99327fdd54891d15a522cb5780fe1e", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/flutter_local_notifications-18.0.1/ios/Classes/ActionEventSink.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e987887e38aa02073e9ebde6100646208a2", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/flutter_local_notifications-18.0.1/ios/Classes/ActionEventSink.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98ce416de311f6514363f33241e2389109", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/flutter_local_notifications-18.0.1/ios/Classes/Converters.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e987be5113da476df02d493f406d9bfcfa2", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/flutter_local_notifications-18.0.1/ios/Classes/Converters.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98b44b81a7ff28c468b03d75b740c8af54", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/flutter_local_notifications-18.0.1/ios/Classes/FlutterEngineManager.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9838c18e1211893800183f69f65535ad0f", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/flutter_local_notifications-18.0.1/ios/Classes/FlutterEngineManager.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9821879530d5069ff9e9c30be658c2a9fb", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/flutter_local_notifications-18.0.1/ios/Classes/FlutterLocalNotificationsPlugin.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e981590763427e4dda7c8deff8d8ae86bf9", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/flutter_local_notifications-18.0.1/ios/Classes/FlutterLocalNotificationsPlugin.m", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98513f3d69d4de673570516be177cc1927", "name": "Classes", "path": "Classes", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e98198eef0bfc03902019a0d29f34c9053a", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/flutter_local_notifications-18.0.1/ios/Resources/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e985d6be67c227e99e4845d1aeeb6780e30", "name": "Resources", "path": "Resources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98e656bbe21134468259972d8128d7ecc6", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98e5e7435354225174f49998d11dd87a5d", "name": "flutter_local_notifications", "path": "flutter_local_notifications", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98c11705d065f6abb0e6924b2eb0530658", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9857980b01a6021de971795ea146fe727e", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9805cb06f8eea2332c12d34cf5ce16d4b0", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98721c1fc552303d186e48ba5aca0a0f98", "name": "test_notifictions", "path": "test_notifictions", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98ea1ac06b8e551e4ef4b781922a5ccd6b", "name": "StudioProjects", "path": "StudioProjects", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e984cddd6a59ad9f71020d7c6bed74764e0", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9898102ffc8cfa95abd8dd68589b3a1fe9", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98ffc515e1b8544e36ec8cf881eae2d454", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98033838f7d84f462b3a09a219b636898c", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98cfc00220bd5de34a98a411f05f17d521", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9806840db9d96a274cb5bd917874dcf124", "name": "..", "path": "../../../../../../../.pub-cache/hosted/pub.dev/flutter_local_notifications-18.0.1/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e981a28a729bf1c540f773098fb397dbac4", "path": "../../../../../../../.pub-cache/hosted/pub.dev/flutter_local_notifications-18.0.1/ios/flutter_local_notifications.podspec", "sourceTree": "<group>", "type": "file"}, {"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e9805b5685a7a34c6b4099fb8da92f5838f", "path": "../../../../../../../.pub-cache/hosted/pub.dev/flutter_local_notifications-18.0.1/LICENSE", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98cb7bf80b2da794090983420de9929166", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e984a1184c1bb3d826090ceccbd5268bfef", "path": "flutter_local_notifications.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98f7334512e4d41236bb265c06135d0689", "path": "flutter_local_notifications-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98773e3e8c55d62079bb7acfe06d2c8b17", "path": "flutter_local_notifications-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9807125964a173cad33ac0cb60048e7eca", "path": "flutter_local_notifications-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98ea81ca4dd51f26cb14dfb756ca4c8441", "path": "flutter_local_notifications-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98208ed94adde2b862a1d2bb020923d34d", "path": "flutter_local_notifications.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98e5d6617c5bde1311304f26a1e92a935e", "path": "flutter_local_notifications.release.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98fb8e93eccceda04252e2edacf04640ff", "path": "ResourceBundle-flutter_local_notifications_privacy-flutter_local_notifications-Info.plist", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9801b5821ee95b98a869874a657e3f4c98", "name": "Support Files", "path": "../../../../Pods/Target Support Files/flutter_local_notifications", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e984bfd246446d95c81f2c5865e5c847261", "name": "flutter_local_notifications", "path": "../.symlinks/plugins/flutter_local_notifications/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e981f856d35767a51564961b7343543ecab", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_osm_plugin-1.3.6/ios/flutter_osm_plugin/Sources/flutter_osm_plugin/Extension.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9888a4bf3e8beda28b74e3976d4006b515", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_osm_plugin-1.3.6/ios/flutter_osm_plugin/Sources/flutter_osm_plugin/FlutterOsmPlugin.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e981616333fdbd86f35f2126900f1a04649", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_osm_plugin-1.3.6/ios/flutter_osm_plugin/Sources/flutter_osm_plugin/FlutterOsmPlugin.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9849005745b0931f5383ae79d347e28280", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_osm_plugin-1.3.6/ios/flutter_osm_plugin/Sources/flutter_osm_plugin/MapviewFactory.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e985e39e3af2858bb8ab328745a295b80a1", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_osm_plugin-1.3.6/ios/flutter_osm_plugin/Sources/flutter_osm_plugin/MyMapView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98818c0740f614d01ca21f49a18d20b6c7", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_osm_plugin-1.3.6/ios/flutter_osm_plugin/Sources/flutter_osm_plugin/RoadManager.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98aeb3671168e2f62c0ded735b8eae5116", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_osm_plugin-1.3.6/ios/flutter_osm_plugin/Sources/flutter_osm_plugin/SwiftFlutterOsmPlugin.swift", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e986d6dfa2ab69a3785c23f1087307ad40b", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_osm_plugin-1.3.6/ios/flutter_osm_plugin/Sources/flutter_osm_plugin/map_view/osm_map.swift", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98736b519016cd145e28334e405a3c0ecf", "name": "map_view", "path": "map_view", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98dc5067fdf56db157802036086f5e0f36", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_osm_plugin-1.3.6/ios/flutter_osm_plugin/Sources/flutter_osm_plugin/models/CustomTiles.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98c60b5829f3fef699b92963c45eca8111", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_osm_plugin-1.3.6/ios/flutter_osm_plugin/Sources/flutter_osm_plugin/models/GeoPointMap.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e982e549cf503fa3d9ff956ab4bdf15a5b9", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_osm_plugin-1.3.6/ios/flutter_osm_plugin/Sources/flutter_osm_plugin/models/GeoPointRadians.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9880d731e67cde4499c0b5a28e353f37ad", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_osm_plugin-1.3.6/ios/flutter_osm_plugin/Sources/flutter_osm_plugin/models/Math.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98c0b49efff2b72237afc4135e2f31d641", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_osm_plugin-1.3.6/ios/flutter_osm_plugin/Sources/flutter_osm_plugin/models/Road.swift", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98be4e1420646852380b7cf82c6f5a08ea", "name": "models", "path": "models", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9861b85dc55794b9b829191718c05426c0", "name": "flutter_osm_plugin", "path": "flutter_osm_plugin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e986dffc03aa717b8197a0b2a7a81538e03", "name": "Sources", "path": "Sources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98fd2bffb43b8f365aa8ee2bec24d27097", "name": "flutter_osm_plugin", "path": "flutter_osm_plugin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e989e63819e669ea3e8b22340cfb6c99a42", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9854f85772a97ac2b85922e2b0421a6bd0", "name": "flutter_osm_plugin", "path": "flutter_osm_plugin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98930259935c5ec133b42bae9d629ccd91", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98814bd0d284493056995318fe4248611f", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98307451b98e37aeb59ce4b84f01bf2ca8", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e982fa1e4f7b8f11fafbf27a10e322cc0bc", "name": "test_notifictions", "path": "test_notifictions", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9802922485a7c6df91922d264ca0299e13", "name": "StudioProjects", "path": "StudioProjects", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9869be17e34832a2d1c8d0c7a5aa41ab34", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98763cc7c5a11ff165cb7805a823c2d6b4", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "text.json", "guid": "bfdfe7dc352907fc980b868725387e989c9feb22313725b181fbbda8de329827", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/flutter_osm_plugin-1.3.6/ios/Assets/en.json", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98a99846e5d6eb2eda7cca6bb978ed4f41", "name": "Assets", "path": "Assets", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98d944a91f1c69e335803531849248c962", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e987e7492437afa931a2374879e5d2f9f41", "name": "flutter_osm_plugin", "path": "flutter_osm_plugin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98d92e8363e698df36fc337d26fed987cc", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98f03f901364885069c441bd4246c473db", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98a88e10b319a582cadb62ccdf27d28fa8", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9894f38da581520c9e5d07641d9d836d73", "name": "test_notifictions", "path": "test_notifictions", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e980515e5c76f27b5ccec86b901a399cec7", "name": "StudioProjects", "path": "../../StudioProjects", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98d406ea06b90ab359900d03f6422e1758", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e988623835d56850e21774a174bcd89d697", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98fe0f835f221d6df009685ee4ed107f80", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9843058719e205f75dd832fcab35f5e4fa", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e984d74196e114d8877d78398a92d873835", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98ad369dfe2ebce96a036d9abd1753e740", "name": "..", "path": "../../../../../../../.pub-cache/hosted/pub.dev/flutter_osm_plugin-1.3.6/ios/flutter_osm_plugin/Sources", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e980106afece9c3a9e2ac026dc6412f2b52", "path": "../../../../../../../.pub-cache/hosted/pub.dev/flutter_osm_plugin-1.3.6/ios/flutter_osm_plugin.podspec", "sourceTree": "<group>", "type": "file"}, {"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e9861d81e6c53b5acc5aa015929b77aa12b", "path": "../../../../../../../.pub-cache/hosted/pub.dev/flutter_osm_plugin-1.3.6/LICENSE", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9893bbd3758245c25ca901bc46ed5323a8", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e982e6e0984e6811970d2e91fb788c37a01", "path": "flutter_osm_plugin.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98f3acdbe694e2683da33b6c147aa29292", "path": "flutter_osm_plugin-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e986641a94dc14e89fad08283c24b97bc6a", "path": "flutter_osm_plugin-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9870adeaf46bf98dc08ac01b71e7b8f10f", "path": "flutter_osm_plugin-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e989542105a3ea5d05b88c6ad26b24b72d5", "path": "flutter_osm_plugin-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98708b7f478320ba0258ec9e94a0c35c03", "path": "flutter_osm_plugin.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98558247d3c9e61bee0141ee29e5b6b29f", "path": "flutter_osm_plugin.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98979c7821e4031968b3205a208e32f37c", "name": "Support Files", "path": "../../../../Pods/Target Support Files/flutter_osm_plugin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9852e72c9e06f8661d9232226a39f76082", "name": "flutter_osm_plugin", "path": "../.symlinks/plugins/flutter_osm_plugin/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9816240e2e068a5687ec7c9c5e392d4e2d", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/fluttertoast-8.2.10/ios/Classes/FluttertoastPlugin.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e986e463e940c079790b585f7fd15aed92e", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/fluttertoast-8.2.10/ios/Classes/FluttertoastPlugin.m", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98de5dcb0945e42a0f762a85809a2860c6", "name": "Classes", "path": "Classes", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e987590ee6de25daa57d0b856694a259fbe", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/fluttertoast-8.2.10/ios/Resources/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9868b7823be067877448f8434dd434116d", "name": "Resources", "path": "Resources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9897b04bde5a479095737766250903b88b", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e980841e66132af76aa0c672ebe6e695976", "name": "fluttertoast", "path": "fluttertoast", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e988fd062e20016208a193dde01767c1d29", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9808e4f0fa9b2c29302fd97b1f2d70d202", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98b96886af9f275c8995eee2a469f8cd3f", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e988116b88d626d1be56f089d554f7f619f", "name": "test_notifictions", "path": "test_notifictions", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98f8956a037d4bb2dfeb2cb9d30bc201c0", "name": "StudioProjects", "path": "StudioProjects", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e986d09c58677a79f4f1a13114d26845a9a", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e988986af010202cc72716a6e79201be401", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9832b65b4b50373f1f98c9fb9d63cb70fc", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98f7404693c3ba49b8e9390bfd1948eca5", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98bc9ba68eb03d8bdf8d6aa92194a06b14", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98ef316e8562292d43ee2a08b3cc0fdafd", "name": "..", "path": "../../../../../../../.pub-cache/hosted/pub.dev/fluttertoast-8.2.10/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e983041406a995e23622f2fd45762458f89", "path": "../../../../../../../.pub-cache/hosted/pub.dev/fluttertoast-8.2.10/ios/fluttertoast.podspec", "sourceTree": "<group>", "type": "file"}, {"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e980e2c8d0ae527489bd33cbc466b5d6936", "path": "../../../../../../../.pub-cache/hosted/pub.dev/fluttertoast-8.2.10/LICENSE", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9800847e908ec1f83019b6da7915e1a701", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e980948203f739bbecf7efc3175755a4c23", "path": "fluttertoast.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9818532deac6cd7170c3e10c809957825f", "path": "fluttertoast-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e987fd385e57a8a1947db3f261d1bae9b6c", "path": "fluttertoast-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e988a204e792c6cd37ece19387bc4b1980e", "path": "fluttertoast-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9830efab1c2b2ac09f6bff1dc87c21903c", "path": "fluttertoast-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98f64c8bf4bcec4739b0e4491f3e8e44dd", "path": "fluttertoast.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98baaa480949ccbd492ca48c5c2221527d", "path": "fluttertoast.release.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e985190b522b6543324c3d1a514ab877c12", "path": "ResourceBundle-fluttertoast_privacy-fluttertoast-Info.plist", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e980b4d715550bcccfc0e66b0113656f967", "name": "Support Files", "path": "../../../../Pods/Target Support Files/fluttertoast", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98a68572cd5615451a5d2d5652923221e6", "name": "fluttertoast", "path": "../.symlinks/plugins/fluttertoast/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98a035f86b42e34f2583ed54eaf568ee43", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/geocoding_ios-3.0.1/ios/Classes/GeocodingHandler.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e985ea63d807c42ac9c5ba772f41690b458", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/geocoding_ios-3.0.1/ios/Classes/GeocodingHandler.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98a9cc0beddfc4b24ea0c878f27088af98", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/geocoding_ios-3.0.1/ios/Classes/GeocodingPlugin.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98fb22939d96d2e1153f0de774c87353af", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/geocoding_ios-3.0.1/ios/Classes/GeocodingPlugin.m", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e981f2852242c3776cb5c6ea04683311e23", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/geocoding_ios-3.0.1/ios/Classes/Extensions/CLPlacemarkExtensions.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9858ead7788f0e1f471bfdd5548142a846", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/geocoding_ios-3.0.1/ios/Classes/Extensions/CLPlacemarkExtensions.m", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98e0879d26e4a92ccc173061f450f2c5e3", "name": "Extensions", "path": "Extensions", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e989f3758a9b5e9fa687a6ba8a438e2f91d", "name": "Classes", "path": "Classes", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e98a6ffabf100f8f7f5bfa97ec7d41671f8", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/geocoding_ios-3.0.1/ios/Resources/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e989ab832c6ca2db0868a96eb85286d5b29", "name": "Resources", "path": "Resources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98e403d396f37677d808fbb5dd8291aa3a", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e981fde862eecbd6d8fc1f92a5e023a35a4", "name": "geocoding_ios", "path": "geocoding_ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98794ff35183d7f67777d3af8fbb9dd8e0", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9835b28d34bab7ef3e75d7fda5748210c3", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9879b98c2d1112682bacc4d510f4484329", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98623441af312d4e099a89c7d32d88b5d4", "name": "test_notifictions", "path": "test_notifictions", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98bb1236447800ecd0af37b7fc00398a8a", "name": "StudioProjects", "path": "StudioProjects", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98f9e99c1f130be39220c207124510fbe1", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e985d85f46cea334381d58c672db2e45583", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e987987a73eed5d5314a6a37cd875d47420", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e989bb5d94f7ba03f996ee8083d8e1c1d6f", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9807594569ba4fc689f736dda927177968", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98bd29b924a67cf6fcc9908c73a9bb6a26", "name": "..", "path": "../../../../../../../.pub-cache/hosted/pub.dev/geocoding_ios-3.0.1/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e98510965de7eb321cbba2509d3c85c4fde", "path": "../../../../../../../.pub-cache/hosted/pub.dev/geocoding_ios-3.0.1/ios/geocoding_ios.podspec", "sourceTree": "<group>", "type": "file"}, {"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e982ba9fea455def6cd46aeac51b4d6d6cf", "path": "../../../../../../../.pub-cache/hosted/pub.dev/geocoding_ios-3.0.1/LICENSE", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9853c347134a9a6f6cc62c973f3260f5e7", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e98a2c566c97c3d4273bfaade512c19ee49", "path": "geocoding_ios.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98b8f79839bacd6808796b435b9d7e900c", "path": "geocoding_ios-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98735d59a27de7c7bc1af4de27a168efbc", "path": "geocoding_ios-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98677061ffd829d93c14832047ea43647c", "path": "geocoding_ios-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98375910c51f1c9467cd692de70ab1f191", "path": "geocoding_ios-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e988dbf56333bc6b9d410d6ce9534b6e731", "path": "geocoding_ios.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98829eeda16a305d67bc8d74ff68b0b15a", "path": "geocoding_ios.release.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e981fdeae8790d41933c30d062142f43773", "path": "ResourceBundle-geocoding_ios_privacy-geocoding_ios-Info.plist", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e985310bda545be74cdef444db26aba86a6", "name": "Support Files", "path": "../../../../Pods/Target Support Files/geocoding_ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98ed3592e61600006846fb799eaabb3aef", "name": "geocoding_ios", "path": "../.symlinks/plugins/geocoding_ios/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9890211958b49521c1e8980c95da1e3d88", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/geolocator_apple-2.3.8+1/ios/Classes/geolocator-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98c2f094fa596c62f13e341951efe2cda9", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/geolocator_apple-2.3.8+1/ios/Classes/GeolocatorPlugin.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98eaf3f5a3cb34f28246c4e1e256aed418", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/geolocator_apple-2.3.8+1/ios/Classes/GeolocatorPlugin.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98e073445b1c3e80eff6ff4b50d9ac6fc1", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/geolocator_apple-2.3.8+1/ios/Classes/GeolocatorPlugin_Test.h", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98b6e96737406a29d847b4cc3b88679bab", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/geolocator_apple-2.3.8+1/ios/Classes/Constants/ErrorCodes.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e982c8204122c75cec2f7ee8b73b73092c3", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/geolocator_apple-2.3.8+1/ios/Classes/Constants/ErrorCodes.m", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98ab62c0059f0f030eddcfa8f9c19ca5bf", "name": "Constants", "path": "Constants", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98fd6c7f2f49cd41890f74e698a2fc0ecf", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/geolocator_apple-2.3.8+1/ios/Classes/Handlers/GeolocationHandler.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98d518575ef80bccc3ae352893519bd8a1", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/geolocator_apple-2.3.8+1/ios/Classes/Handlers/GeolocationHandler.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e988941ba47086eb406ada17c0862aee756", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/geolocator_apple-2.3.8+1/ios/Classes/Handlers/GeolocationHandler_Test.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98f1b4a001df4509e6f8a882151d095fa7", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/geolocator_apple-2.3.8+1/ios/Classes/Handlers/LocationAccuracyHandler.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e986a1ad19c5b790cedaf1c5aa3c97b580d", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/geolocator_apple-2.3.8+1/ios/Classes/Handlers/LocationAccuracyHandler.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9827c6b6409b518bd74f5267a6ed3a22cb", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/geolocator_apple-2.3.8+1/ios/Classes/Handlers/LocationServiceStreamHandler.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e981dce628457c14d7522ed8469a50a9272", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/geolocator_apple-2.3.8+1/ios/Classes/Handlers/LocationServiceStreamHandler.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98634de7e6f46c4a2db42fc4a5555990c0", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/geolocator_apple-2.3.8+1/ios/Classes/Handlers/PermissionHandler.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9881e773ae1ff79629cec3fe5efff33e92", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/geolocator_apple-2.3.8+1/ios/Classes/Handlers/PermissionHandler.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e988040c36d40b6f7cb038a0169ae25b010", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/geolocator_apple-2.3.8+1/ios/Classes/Handlers/PositionStreamHandler.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e985173ea4c393eb4f1d560b66a87f59be5", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/geolocator_apple-2.3.8+1/ios/Classes/Handlers/PositionStreamHandler.m", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98eb38345cabb5b7a9c7d0f4d048f4e04c", "name": "Handlers", "path": "Handlers", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98d14b73d95ca8e459f7007f31c6383863", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/geolocator_apple-2.3.8+1/ios/Classes/Utils/ActivityTypeMapper.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98ea30e5fbf9c510c6644790e495507a6e", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/geolocator_apple-2.3.8+1/ios/Classes/Utils/ActivityTypeMapper.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98cabe151174705bf4d3f54421b5efbd94", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/geolocator_apple-2.3.8+1/ios/Classes/Utils/AuthorizationStatusMapper.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e985eb04e38c2b4c66531279e5a6239222f", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/geolocator_apple-2.3.8+1/ios/Classes/Utils/AuthorizationStatusMapper.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9833ff5368e1f87f199434e72fb190e99f", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/geolocator_apple-2.3.8+1/ios/Classes/Utils/LocationAccuracyMapper.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9859152c6677061bd0fc4ffc968e4b75f1", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/geolocator_apple-2.3.8+1/ios/Classes/Utils/LocationAccuracyMapper.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e980ddcfda9df91084df6f770c506811400", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/geolocator_apple-2.3.8+1/ios/Classes/Utils/LocationDistanceMapper.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e983750bf8203b2a1244feededb0533f61c", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/geolocator_apple-2.3.8+1/ios/Classes/Utils/LocationDistanceMapper.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98c04608ffb7ed03e4d0e313127072b561", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/geolocator_apple-2.3.8+1/ios/Classes/Utils/LocationMapper.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9824ee09df0691cc772034559881a42f44", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/geolocator_apple-2.3.8+1/ios/Classes/Utils/LocationMapper.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e980bf2885c17d6128f091317967c52211c", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/geolocator_apple-2.3.8+1/ios/Classes/Utils/PermissionUtils.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9820ce1d45a2bbbeb58c2b16d1454afef5", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/geolocator_apple-2.3.8+1/ios/Classes/Utils/PermissionUtils.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98309eba574350178cc6ce0f75a26ac709", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/geolocator_apple-2.3.8+1/ios/Classes/Utils/ServiceStatus.h", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e982b66d6c7e5c082b1cc66edf4983f8fd3", "name": "Utils", "path": "Utils", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98e90cc1c114d9f673551f05391abfd5e6", "name": "Classes", "path": "Classes", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e98d3f90f086d722ae7b4de1e8840830efb", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/geolocator_apple-2.3.8+1/ios/Resources/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98a4cceffd11da42a8dbc5fea0abcb04df", "name": "Resources", "path": "Resources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98d4ab017d547efe5ea97219dd60f63d05", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98d2b6eb8a764470bacc66ff9634c9da28", "name": "geolocator_apple", "path": "geolocator_apple", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9829b339e10c768553ea7975a1c8c5d3ff", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98250df9461b24f697d6d8eef92e9bcffb", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98e6db227e67a04bbb1de04552cdb8aafa", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98ecbf76d59158880febc823a10236bb22", "name": "test_notifictions", "path": "test_notifictions", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e988ad081dfe2abe3bddf7a0d86de01cff9", "name": "StudioProjects", "path": "StudioProjects", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98b40df016ec1a28096c4f90492ecb6d3f", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98011865bf27327b985d8e63b0f779adc2", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e980893d5fcca7efa3836d18dcfeeb7bda1", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e980c56ea7c0f2283b9e43f27633ef35f87", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98df00b2f6f4d2b42fd269c8e71ff67cad", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98be79456913db73a0810984e760b82a17", "name": "..", "path": "../../../../../../../.pub-cache/hosted/pub.dev/geolocator_apple-2.3.8+1/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e98a717e85721f61ff847d881d6d4e2642f", "path": "../../../../../../../.pub-cache/hosted/pub.dev/geolocator_apple-2.3.8+1/ios/geolocator_apple.podspec", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e98505d40e0b441b7a0d25a4a2faf530317", "path": "../../../../../../../.pub-cache/hosted/pub.dev/geolocator_apple-2.3.8+1/ios/Classes/GeolocatorPlugin.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e98263be726d2b77ae0d168c308e4eda8ff", "path": "../../../../../../../.pub-cache/hosted/pub.dev/geolocator_apple-2.3.8+1/LICENSE", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e989eace81eea681927af82a37b300b26fe", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e986c469b2c2f6db7a8742d32868da4fb6f", "path": "geolocator_apple.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9882dc12a81ba4d5c2ee082c22e4e85d03", "path": "geolocator_apple-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e9898dec177a42fe6c7d26362b6b30757f1", "path": "geolocator_apple-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98ae0f4f65e03344fbf3df11b53afe2136", "path": "geolocator_apple-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e9847b6654c4a6fc049a4445bac224f4a05", "path": "geolocator_apple.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e986b53a6a644977c0881ce0b2e3ff4d8e8", "path": "geolocator_apple.release.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e9863b903a073a6d2686b6e37015fd77971", "path": "ResourceBundle-geolocator_apple_privacy-geolocator_apple-Info.plist", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9890e54c3895f2d47336ca17c5c32c853a", "name": "Support Files", "path": "../../../../Pods/Target Support Files/geolocator_apple", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98b27784fb2b508815a232e9fc62b8ddb7", "name": "geolocator_apple", "path": "../.symlinks/plugins/geolocator_apple/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e987774a3cb9b8e5443c9b192f334494fd1", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/google_maps_flutter_ios-2.13.2/ios/Classes/FGMClusterManagersController.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98c999d60929dc61ba814fafe6a3a028ca", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/google_maps_flutter_ios-2.13.2/ios/Classes/FGMClusterManagersController.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e984007836ed8442927876159d1657fd020", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/google_maps_flutter_ios-2.13.2/ios/Classes/FGMMarkerUserData.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98ebc8e5cce4f986a8f4614982dc53b85f", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/google_maps_flutter_ios-2.13.2/ios/Classes/FGMMarkerUserData.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98d10172817df5af66ac69744bf4eaac35", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/google_maps_flutter_ios-2.13.2/ios/Classes/FLTGoogleMapHeatmapController.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98054be2be02a221c7afec340ea8c6d70d", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/google_maps_flutter_ios-2.13.2/ios/Classes/FLTGoogleMapHeatmapController.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98485a296bced6d2f886578cca484ca068", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/google_maps_flutter_ios-2.13.2/ios/Classes/FLTGoogleMapJSONConversions.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e984783fa098596ad345f76177c490a6cfa", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/google_maps_flutter_ios-2.13.2/ios/Classes/FLTGoogleMapJSONConversions.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9821880937e6d729c39b3de023aca7c64e", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/google_maps_flutter_ios-2.13.2/ios/Classes/FLTGoogleMapsPlugin.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98b0652ebbec4a175c71932a843a992f66", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/google_maps_flutter_ios-2.13.2/ios/Classes/FLTGoogleMapsPlugin.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98974a3cb2ab07431d4c78c19e252a467e", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/google_maps_flutter_ios-2.13.2/ios/Classes/FLTGoogleMapTileOverlayController.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e980399d56f4ea3d38805724b0b2a55e15e", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/google_maps_flutter_ios-2.13.2/ios/Classes/FLTGoogleMapTileOverlayController.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e980bd125909b7a8268e0dde6be6d11700d", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/google_maps_flutter_ios-2.13.2/ios/Classes/google_maps_flutter_ios-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9803ca1a9bde108c9a0862d6acd6bdd774", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/google_maps_flutter_ios-2.13.2/ios/Classes/GoogleMapCircleController.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e987f4c68f4d336266a7f2d4d3580520940", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/google_maps_flutter_ios-2.13.2/ios/Classes/GoogleMapCircleController.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e988955b2d26b11c00abb6fbb4b5f3f0829", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/google_maps_flutter_ios-2.13.2/ios/Classes/GoogleMapController.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e986a0d8ee7779ce30333dde49c0922ee14", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/google_maps_flutter_ios-2.13.2/ios/Classes/GoogleMapController.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98da7618245498a3627aac8728c7e64789", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/google_maps_flutter_ios-2.13.2/ios/Classes/GoogleMapController_Test.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98e2dc20cea0f532da6e2140f63e4f4d05", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/google_maps_flutter_ios-2.13.2/ios/Classes/GoogleMapMarkerController.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9808a0b8c7d918b120c96ee3bc70f79c33", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/google_maps_flutter_ios-2.13.2/ios/Classes/GoogleMapMarkerController.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98353844db9f4ab2b7d10b689ffbd9cd88", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/google_maps_flutter_ios-2.13.2/ios/Classes/GoogleMapMarkerController_Test.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98bcb9561e259567ab4dc311291c8a3940", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/google_maps_flutter_ios-2.13.2/ios/Classes/GoogleMapPolygonController.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e986c3e32335ebfa102504d3ecfa9aa7261", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/google_maps_flutter_ios-2.13.2/ios/Classes/GoogleMapPolygonController.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9801afdf7b20e52fa2d27a38a7cddb3c97", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/google_maps_flutter_ios-2.13.2/ios/Classes/GoogleMapPolylineController.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9820a8e2e29f394754a7aac87b793fc6eb", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/google_maps_flutter_ios-2.13.2/ios/Classes/GoogleMapPolylineController.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9853bdf15aa30ab6b6cf642e4549413a0d", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/google_maps_flutter_ios-2.13.2/ios/Classes/GoogleMapPolylineController_Test.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98b75bef7c7a32ea3a63cd52c73ff5c34d", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/google_maps_flutter_ios-2.13.2/ios/Classes/messages.g.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9838d26a9f0ab89cdd7e1d7ca4894ed9d2", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/google_maps_flutter_ios-2.13.2/ios/Classes/messages.g.m", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9806cee7f5159d897348a5188012db5d61", "name": "Classes", "path": "Classes", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e98fb6f4a1e86347d16e41434ddeb4ac547", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/google_maps_flutter_ios-2.13.2/ios/Resources/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e989d1beadf4bcf267931ee4dce993e2ba0", "name": "Resources", "path": "Resources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98b357c7895d6c46ab127320e31557b813", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98eb0cc58888bbfc3d1d71d44945ec052c", "name": "google_maps_flutter_ios", "path": "google_maps_flutter_ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e987e0344d52e55338bf1d1fa43a05c046c", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e983c5aa59d9c98b3a6e2da9c80bcc92353", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98311223e7dd4f657f42dc671ec3584a09", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98ec0d3e89790da5dca43bd829a53aa2f6", "name": "test_notifictions", "path": "test_notifictions", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e981880a019f3a481fec3ab800c0a765ed5", "name": "StudioProjects", "path": "StudioProjects", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9879cc157ad9eb72e621bbdb40e592ac89", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e984db1dffee1d9157f1be49e6151f9ff23", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9853a8773a92bd799577a2d787f3253f8f", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e988add0e369995df921c14b1416342c968", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e982c15caaab9cd11d4b52413970c3e3f08", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98d48b2ebc8f5635764996f9c150221bee", "name": "..", "path": "../../../../../../../.pub-cache/hosted/pub.dev/google_maps_flutter_ios-2.13.2/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e983cbe9a6b9c8adef5156d961ab9f3b570", "path": "../../../../../../../.pub-cache/hosted/pub.dev/google_maps_flutter_ios-2.13.2/ios/Classes/google_maps_flutter_ios.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e988371a7ad70a2a996f895c8b3f43b7093", "path": "../../../../../../../.pub-cache/hosted/pub.dev/google_maps_flutter_ios-2.13.2/ios/google_maps_flutter_ios.podspec", "sourceTree": "<group>", "type": "file"}, {"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e98c1c10501c1f7fa85f20c0a3fcc2be3a0", "path": "../../../../../../../.pub-cache/hosted/pub.dev/google_maps_flutter_ios-2.13.2/LICENSE", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e984d73009eac35f819e4fd076d48e927cd", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e98b5873bcffea6e01f907327c4af51b4ad", "path": "google_maps_flutter_ios.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98d08d7a777731222b9884c3270f7fa51d", "path": "google_maps_flutter_ios-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98177e29ed5cc9ea17587cb73d7187327c", "path": "google_maps_flutter_ios-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98333080363a4ac143b68f9971579e3d36", "path": "google_maps_flutter_ios-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e988238c136bfe4506963163e44fae4b14a", "path": "google_maps_flutter_ios.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e981c24c068b057b1ca3beb287b70e4ad2d", "path": "google_maps_flutter_ios.release.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98092258003b781669091fde620b918904", "path": "ResourceBundle-google_maps_flutter_ios_privacy-google_maps_flutter_ios-Info.plist", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e988d7a78a6a68e4ebde9fb0ac84ad9d583", "name": "Support Files", "path": "../../../../Pods/Target Support Files/google_maps_flutter_ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e986b275c6daec7e68ac9b8601238fa9bb3", "name": "google_maps_flutter_ios", "path": "../.symlinks/plugins/google_maps_flutter_ios/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "file.xib", "guid": "bfdfe7dc352907fc980b868725387e98e6df3de7d38a044a2bfae148050802d2", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/google_mobile_ads-5.2.0/ios/Classes/NativeTemplates/GoogleAdsMobileIosNativeTemplates/GADTFullScreenTemplateView.xib", "sourceTree": "<group>", "type": "file"}, {"fileType": "file.xib", "guid": "bfdfe7dc352907fc980b868725387e982bf1e3a91933521a6ae64b1b5599399f", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/google_mobile_ads-5.2.0/ios/Classes/NativeTemplates/GoogleAdsMobileIosNativeTemplates/GADTMediumTemplateView.xib", "sourceTree": "<group>", "type": "file"}, {"fileType": "file.xib", "guid": "bfdfe7dc352907fc980b868725387e981481dc2f199c9d36fc3fb3e66bd241ef", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/google_mobile_ads-5.2.0/ios/Classes/NativeTemplates/GoogleAdsMobileIosNativeTemplates/GADTSmallTemplateView.xib", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98855135ec7bc3bef3810774f3d3acc527", "name": "GoogleAdsMobileIosNativeTemplates", "path": "GoogleAdsMobileIosNativeTemplates", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e987e14b88c6d8dbb817c16614c6904dbcc", "name": "NativeTemplates", "path": "NativeTemplates", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98660d20e9faaf6b2253c36f681b115262", "name": "Classes", "path": "Classes", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e986edeba895e0eaa3b5c9cdc007db8c6d7", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98a745d8f2f0187778c7aa01d73a8558c9", "name": "google_mobile_ads", "path": "google_mobile_ads", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98390ee13f811f7b2463df8ca0a7b89584", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e985d7cbdc5661ba4b4ccfdd8de6e0c8df8", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e983fb1efc0d897f02620e344e96ff43654", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9897d9d22b8af9ec5b9bed401f30f63274", "name": "test_notifictions", "path": "test_notifictions", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9846174cfcf936b701dbce1fd9efb298cf", "name": "StudioProjects", "path": "StudioProjects", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98f7bbf09ef50de3564b71e24aecea9c27", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e982525d28e904836e93818451c5d49349b", "name": "..", "path": ".pub-cache", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9837c80a64f8cd6e1dfd08824f5ed8b7bd", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/google_mobile_ads-5.2.0/ios/Classes/FLTAd_Internal.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9879fbbde193a96c071b9d41a9fd29f693", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/google_mobile_ads-5.2.0/ios/Classes/FLTAd_Internal.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e982d089b1b7e47b7680d0c7e92f3ecbfca", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/google_mobile_ads-5.2.0/ios/Classes/FLTAdInstanceManager_Internal.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98141ca2b5ac95708441eec635667f0cb4", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/google_mobile_ads-5.2.0/ios/Classes/FLTAdInstanceManager_Internal.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98befd02a999f6acccfcccbcfd3c9f1bb0", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/google_mobile_ads-5.2.0/ios/Classes/FLTAdUtil.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e981fbea663d5349a1421dc7e380c2a3e0e", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/google_mobile_ads-5.2.0/ios/Classes/FLTAdUtil.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9843503e4f7977ceab9a41b32cbd7a54e5", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/google_mobile_ads-5.2.0/ios/Classes/FLTAppStateNotifier.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98d753c9c4a6f0665a0cf4dda4773b4742", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/google_mobile_ads-5.2.0/ios/Classes/FLTAppStateNotifier.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98b3c8dedc7a9871758a62c1f4f6eb14c6", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/google_mobile_ads-5.2.0/ios/Classes/FLTConstants.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9801b41adcdcf607854f6e7b6c9bed1845", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/google_mobile_ads-5.2.0/ios/Classes/FLTGoogleMobileAdsCollection_Internal.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98ca9c9939f75acc5472644e9a329571cd", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/google_mobile_ads-5.2.0/ios/Classes/FLTGoogleMobileAdsCollection_Internal.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98135f2f6d34afdffb7531da26e21b0d3a", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/google_mobile_ads-5.2.0/ios/Classes/FLTGoogleMobileAdsPlugin.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98b08f45da32d9aef80a9f16f74b1f30c7", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/google_mobile_ads-5.2.0/ios/Classes/FLTGoogleMobileAdsPlugin.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e987d432ed5812235aeb4693103dfbd33e9", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/google_mobile_ads-5.2.0/ios/Classes/FLTGoogleMobileAdsReaderWriter_Internal.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e981adb0a28bd546a7338b808c023d0bc17", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/google_mobile_ads-5.2.0/ios/Classes/FLTGoogleMobileAdsReaderWriter_Internal.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98114912b4798ba11b13f0985a66846500", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/google_mobile_ads-5.2.0/ios/Classes/FLTMediationExtras.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98bebee1dbc9d56abc04de618501426436", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/google_mobile_ads-5.2.0/ios/Classes/FLTMediationNetworkExtrasProvider.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e981469f4bd2882a2a0f3fd3b18b28f5246", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/google_mobile_ads-5.2.0/ios/Classes/FLTMobileAds_Internal.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9876227a71f0ab5241598296a95c019d0e", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/google_mobile_ads-5.2.0/ios/Classes/FLTMobileAds_Internal.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9830190ddf05da553fbef954cb433c1c45", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/google_mobile_ads-5.2.0/ios/Classes/FLTNSString.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9821d3f3a75cce276cf96c6a5a31217ebd", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/google_mobile_ads-5.2.0/ios/Classes/FLTNSString.m", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98ed43f4fbc9783efcf8da7a7845e62ccd", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/google_mobile_ads-5.2.0/ios/Classes/NativeTemplates/FLTNativeTemplateColor.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98874d671f8c59e1613ff077956dcc63ce", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/google_mobile_ads-5.2.0/ios/Classes/NativeTemplates/FLTNativeTemplateColor.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e985bb335adc7728cdf2d04ee48ef9d5921", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/google_mobile_ads-5.2.0/ios/Classes/NativeTemplates/FLTNativeTemplateFontStyle.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98418017c31ade29c532b15c01315a498d", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/google_mobile_ads-5.2.0/ios/Classes/NativeTemplates/FLTNativeTemplateFontStyle.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e981fd0ef00bdd05b4e31702ca97f27b87b", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/google_mobile_ads-5.2.0/ios/Classes/NativeTemplates/FLTNativeTemplateStyle.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9885b065886bc758ff90ed440b3274649f", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/google_mobile_ads-5.2.0/ios/Classes/NativeTemplates/FLTNativeTemplateStyle.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9867d3822910cd58a15be14c95023b00fe", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/google_mobile_ads-5.2.0/ios/Classes/NativeTemplates/FLTNativeTemplateTextStyle.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98a3f71631893b01aa2c9c46b61389651d", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/google_mobile_ads-5.2.0/ios/Classes/NativeTemplates/FLTNativeTemplateTextStyle.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98f9cf763ad95df24c1dbfe3f4f1dfde1a", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/google_mobile_ads-5.2.0/ios/Classes/NativeTemplates/FLTNativeTemplateType.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9804230825be3b36f9a91dcecec8be3391", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/google_mobile_ads-5.2.0/ios/Classes/NativeTemplates/FLTNativeTemplateType.m", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98d72d678464555dd6920788b896ddf463", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/google_mobile_ads-5.2.0/ios/Classes/NativeTemplates/GoogleAdsMobileIosNativeTemplates/GADTFullScreenTemplateView.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98b5fb775a55aa7d5c772fb1efa107ec14", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/google_mobile_ads-5.2.0/ios/Classes/NativeTemplates/GoogleAdsMobileIosNativeTemplates/GADTFullScreenTemplateView.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e987b70c006f404bc0805edb9e60ed53410", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/google_mobile_ads-5.2.0/ios/Classes/NativeTemplates/GoogleAdsMobileIosNativeTemplates/GADTMediumTemplateView.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e985cd7024aef4664d757c4f3705a1720ae", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/google_mobile_ads-5.2.0/ios/Classes/NativeTemplates/GoogleAdsMobileIosNativeTemplates/GADTMediumTemplateView.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98053e90acc4151d0d92954455eabe9ef3", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/google_mobile_ads-5.2.0/ios/Classes/NativeTemplates/GoogleAdsMobileIosNativeTemplates/GADTSmallTemplateView.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98abc704960623172546c1d8a6a075e91f", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/google_mobile_ads-5.2.0/ios/Classes/NativeTemplates/GoogleAdsMobileIosNativeTemplates/GADTSmallTemplateView.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98d47d5ae8e4d910139cf140245f610b90", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/google_mobile_ads-5.2.0/ios/Classes/NativeTemplates/GoogleAdsMobileIosNativeTemplates/GADTTemplateView.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98d41b420af9b56656513b903a50d82335", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/google_mobile_ads-5.2.0/ios/Classes/NativeTemplates/GoogleAdsMobileIosNativeTemplates/GADTTemplateView.m", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98949a7e08876cae8f4605704cb036d045", "name": "GoogleAdsMobileIosNativeTemplates", "path": "GoogleAdsMobileIosNativeTemplates", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9899ec728db158cb48e3add5147ca1ff06", "name": "NativeTemplates", "path": "NativeTemplates", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98b46cc657da6daa8fc56f97ffdb7b519c", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/google_mobile_ads-5.2.0/ios/Classes/UserMessagingPlatform/FLTUserMessagingPlatformManager.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e987173d954715fb5444b3e46a35658c71c", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/google_mobile_ads-5.2.0/ios/Classes/UserMessagingPlatform/FLTUserMessagingPlatformManager.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e989534363b78ae7c30172d12b50ad3748f", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/google_mobile_ads-5.2.0/ios/Classes/UserMessagingPlatform/FLTUserMessagingPlatformReaderWriter.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9865d1f625b7dda33b60232f56af7c0292", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/google_mobile_ads-5.2.0/ios/Classes/UserMessagingPlatform/FLTUserMessagingPlatformReaderWriter.m", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98a47bc191a7a347353bed7d9b0cc9a488", "name": "UserMessagingPlatform", "path": "UserMessagingPlatform", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98410c1191daae0555bd44947978ddb608", "name": "Classes", "path": "Classes", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98b353c282670739c373a31ea8df6772f1", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9824eeebab554eff1feee9a88da87bc2d9", "name": "google_mobile_ads", "path": "google_mobile_ads", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9870252bafd17f9f732a09d322ac280480", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e981d72660b23b8585b1767e1ff29c39ed9", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e980fa711d56d48a31ed772f385d31b92e4", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98cfcc37402e42cd59a4618bc52795f0a3", "name": "test_notifictions", "path": "test_notifictions", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e986e9805e50e27943aef04f1a3a4b2e670", "name": "StudioProjects", "path": "StudioProjects", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9835be845129ea960152f327e5c88c529c", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98d8f4a61a09f7cd96ad682880f8993410", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98d3a00b10462cec86a9252c5f94139438", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9879d29290f4eb41edb54cdc7f0184e451", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98f5af855d639e681f402279175f293dff", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98d54901b4292cb3cd40a41a9b6f407330", "name": "..", "path": "../../../../../../../.pub-cache/hosted/pub.dev/google_mobile_ads-5.2.0/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e980095de14f6d0a5cf33e002b1b3579303", "path": "../../../../../../../.pub-cache/hosted/pub.dev/google_mobile_ads-5.2.0/ios/google_mobile_ads.podspec", "sourceTree": "<group>", "type": "file"}, {"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e98e038c762ad55e9f86dc7328ce444af8e", "path": "../../../../../../../.pub-cache/hosted/pub.dev/google_mobile_ads-5.2.0/LICENSE", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e986cb8e74d003d16dbfa02b4a329912edb", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e989a01c6be638600b0d9f506624fdc6e19", "path": "google_mobile_ads.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e981fceeabd5158f02e02baa0aaac4ed711", "path": "google_mobile_ads-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e988d365dae6a9850d1a59bc6a51f323cfa", "path": "google_mobile_ads-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98f7d7c0a62cf1432fc7d04d19190062f8", "path": "google_mobile_ads-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e985c1c5d24232c4eebee66fe54a6683995", "path": "google_mobile_ads-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e989e347ee79bd39a6c4756867db80824e6", "path": "google_mobile_ads.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98b85709f544980d18206a69151bc062bb", "path": "google_mobile_ads.release.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98039ed392f971873ee982976f832162cc", "path": "ResourceBundle-google_mobile_ads-google_mobile_ads-Info.plist", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9862f5fd81c7ed4178c51aa15770de3c35", "name": "Support Files", "path": "../../../../Pods/Target Support Files/google_mobile_ads", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98c53f0744011ac001faa869f3d3ab772f", "name": "google_mobile_ads", "path": "../.symlinks/plugins/google_mobile_ads/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e98358183aa84b67c1140928b138d0f3458", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/image_picker_ios-0.8.12+1/ios/image_picker_ios/Sources/image_picker_ios/Resources/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e989adb1b2e8a9ea622d7715900c7016529", "name": "Resources", "path": "Resources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98c48ed87ab40bd1d2d9c6de591b589901", "name": "image_picker_ios", "path": "image_picker_ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98fcf3e8239f4339863f6be5161bac51ac", "name": "Sources", "path": "Sources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98dca0114f473a31c7ab62e539804900ce", "name": "image_picker_ios", "path": "image_picker_ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98227fa5b148cfd7ab97e435fad144709f", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9833250dc1e540b225d54fe998b870997b", "name": "image_picker_ios", "path": "image_picker_ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98af25e4fc89d98407bd6030d98b729c70", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98f91edbb164568833aad598b1dac6e82f", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98cbb9343488d05eca8cfe92944e127ebd", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98a2701d4cfee6d82bc80d4ac10406a6c2", "name": "test_notifictions", "path": "test_notifictions", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98a4748660fc6cafaac5c6de6b8f082dc5", "name": "StudioProjects", "path": "StudioProjects", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98dd3eae47395628d132fa1fb2f1f2d70f", "name": "..", "path": ".", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98670e078e3f68395238e9c5370dbf27d3", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/image_picker_ios-0.8.12+1/ios/image_picker_ios/Sources/image_picker_ios/FLTImagePickerImageUtil.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98b1f1723bb3433c54268a2093e23a5ef7", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/image_picker_ios-0.8.12+1/ios/image_picker_ios/Sources/image_picker_ios/FLTImagePickerMetaDataUtil.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98e1d12eb8ba0c68da2432d55a3b73a903", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/image_picker_ios-0.8.12+1/ios/image_picker_ios/Sources/image_picker_ios/FLTImagePickerPhotoAssetUtil.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e989569c1b1eb18fd209f40bfc390f301f3", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/image_picker_ios-0.8.12+1/ios/image_picker_ios/Sources/image_picker_ios/FLTImagePickerPlugin.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98c527d506ad47eb8007a9cf5a12b0473a", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/image_picker_ios-0.8.12+1/ios/image_picker_ios/Sources/image_picker_ios/FLTPHPickerSaveImageToPathOperation.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98d5a540d210a30e108776d07da90e918c", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/image_picker_ios-0.8.12+1/ios/image_picker_ios/Sources/image_picker_ios/messages.g.m", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9865090a0a8cd012b68086a50ca3fc16a3", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/image_picker_ios-0.8.12+1/ios/image_picker_ios/Sources/image_picker_ios/include/image_picker_ios-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e985b9037231841e0dc4ae3913b7d5f5d3e", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/image_picker_ios-0.8.12+1/ios/image_picker_ios/Sources/image_picker_ios/include/image_picker_ios/FLTImagePickerImageUtil.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98866401bbed8b13b4bcc2d388d00dc956", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/image_picker_ios-0.8.12+1/ios/image_picker_ios/Sources/image_picker_ios/include/image_picker_ios/FLTImagePickerMetaDataUtil.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98e96577940d35e6846567f18b4ddb1b08", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/image_picker_ios-0.8.12+1/ios/image_picker_ios/Sources/image_picker_ios/include/image_picker_ios/FLTImagePickerPhotoAssetUtil.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9823613329d86aa1765200cc78e6a06071", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/image_picker_ios-0.8.12+1/ios/image_picker_ios/Sources/image_picker_ios/include/image_picker_ios/FLTImagePickerPlugin.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98031352f826bd8bc8bcf7878430c38c3c", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/image_picker_ios-0.8.12+1/ios/image_picker_ios/Sources/image_picker_ios/include/image_picker_ios/FLTImagePickerPlugin_Test.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98974871a50419e8ef153d91ff41fedef8", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/image_picker_ios-0.8.12+1/ios/image_picker_ios/Sources/image_picker_ios/include/image_picker_ios/FLTPHPickerSaveImageToPathOperation.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e987ae3e6dfbabf5ccd59ba1148e366f114", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/image_picker_ios-0.8.12+1/ios/image_picker_ios/Sources/image_picker_ios/include/image_picker_ios/messages.g.h", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e985ad4257326c9fb7f3e550a221b9b5c68", "name": "image_picker_ios", "path": "image_picker_ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98e32b6c4d074a1c4157c170f6f89089e1", "name": "include", "path": "include", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9814ffd5e5799e319f975ac2d9cf8aaf9e", "name": "image_picker_ios", "path": "image_picker_ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9834559b3d3b4a0bf881850f0718fb6419", "name": "Sources", "path": "Sources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9881a718a778e3a2bf4e72bf7b89690cb3", "name": "image_picker_ios", "path": "image_picker_ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98f302984863a7be5bf6c41f6f2cb3763e", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e987092ec5b2fe7dda9186d8fd46d63ed56", "name": "image_picker_ios", "path": "image_picker_ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98e30eda7960bfabf9e0e5109ee9d6891c", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98dcc4d5ba0974677776fc6b1174dbaf01", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98f35867912c540d69693b8299cd123ec1", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98563c9c3648f6d34ab33c104e53875c25", "name": "test_notifictions", "path": "test_notifictions", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98546509cb350aee2129bfd0bd6762a279", "name": "StudioProjects", "path": "StudioProjects", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98bb611cdf631295bce3c4a28c14d5cda7", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98613244d6fc6f8c258b218c88215f38e9", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e987f04398af083d69cad47b91a6f6b4212", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98d046ee29fd181f7f6fce13ab0427b244", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98b5f5e0c1e760b829c9e1921ed3d0a8f9", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98532df6957ac98bfad1771cc4fda22394", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9855fea9ca7eaf21acf9678efbe78e9fad", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98d4bb30573c33bd03cd0127b066444ec9", "name": "..", "path": "../../../../../../../.pub-cache/hosted/pub.dev/image_picker_ios-0.8.12+1/ios/image_picker_ios/Sources", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e982a1ab78a0679871a211d2bf048733a1d", "path": "../../../../../../../.pub-cache/hosted/pub.dev/image_picker_ios-0.8.12+1/ios/image_picker_ios.podspec", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e988afdafb25a70fea4be6faf3fdf7e3ef5", "path": "../../../../../../../.pub-cache/hosted/pub.dev/image_picker_ios-0.8.12+1/ios/image_picker_ios/Sources/image_picker_ios/include/ImagePickerPlugin.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e98762e7ebe815843bfb8bc95aaea487208", "path": "../../../../../../../.pub-cache/hosted/pub.dev/image_picker_ios-0.8.12+1/LICENSE", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e981f01a936483c2415f9c2968de582af0b", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e98c142d0ccc359541543648d774b3262c5", "path": "image_picker_ios.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e983aab783023393c357fd4aa80e3d1266c", "path": "image_picker_ios-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e980f0b432685bc5084815f0f56b689adb3", "path": "image_picker_ios-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98cc3f013e6e24535b2eb715fc52b439a8", "path": "image_picker_ios-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e981bdf105948adcea06ace511d783118c2", "path": "image_picker_ios.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e981db22a70d0af4abe65bfd462a7a5ddd9", "path": "image_picker_ios.release.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e982bccf751a6c50fad2aa72755b1abc3e4", "path": "ResourceBundle-image_picker_ios_privacy-image_picker_ios-Info.plist", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e980779768ee9ceb0239d13d60c07c474b9", "name": "Support Files", "path": "../../../../Pods/Target Support Files/image_picker_ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98dd5e496348f27c59de11afa9dd2f36a9", "name": "image_picker_ios", "path": "../.symlinks/plugins/image_picker_ios/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98f15f211f90c40cefe191eb398ec646c0", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/in_app_purchase_storekit-0.3.20+2/darwin/Classes/FIAObjectTranslator.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e980aa9f05f68b3964076a6200c99a12a93", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/in_app_purchase_storekit-0.3.20+2/darwin/Classes/FIAObjectTranslator.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98bf0e7b3ebed12d4ea9a9beb1591c5e84", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/in_app_purchase_storekit-0.3.20+2/darwin/Classes/FIAPaymentQueueHandler.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e980bab28d463fb5d3d03e44084d401e048", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/in_app_purchase_storekit-0.3.20+2/darwin/Classes/FIAPaymentQueueHandler.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e980c3b0d7920e35de86897dd752e03fcc6", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/in_app_purchase_storekit-0.3.20+2/darwin/Classes/FIAPPaymentQueueDelegate.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e987b8f75a5e6a587bcc8cf45def06cd0e8", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/in_app_purchase_storekit-0.3.20+2/darwin/Classes/FIAPPaymentQueueDelegate.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98002c6aa7213b198b7aff128dc303e158", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/in_app_purchase_storekit-0.3.20+2/darwin/Classes/FIAPReceiptManager.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e988ff3e3c906a4169ab7ab383db1a0385f", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/in_app_purchase_storekit-0.3.20+2/darwin/Classes/FIAPReceiptManager.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98ef8e4349690672e4c5d6db1f1fd06ec2", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/in_app_purchase_storekit-0.3.20+2/darwin/Classes/FIAPRequestHandler.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98b6822120018a9119cba967123a945218", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/in_app_purchase_storekit-0.3.20+2/darwin/Classes/FIAPRequestHandler.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98b01a9f4a1945c31888437bb7f3972918", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/in_app_purchase_storekit-0.3.20+2/darwin/Classes/FIATransactionCache.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e989e008117575fd5820e45ca0b3af92cb1", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/in_app_purchase_storekit-0.3.20+2/darwin/Classes/FIATransactionCache.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9855432e55faa3da5b780ff114d9678ffe", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/in_app_purchase_storekit-0.3.20+2/darwin/Classes/in_app_purchase_storekit.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9850c0a3f13458dc9ce1bd1e5eac7a5a55", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/in_app_purchase_storekit-0.3.20+2/darwin/Classes/in_app_purchase_storekit-Bridging-Header.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98a0ee3001350bdb7ae620023cacfaa740", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/in_app_purchase_storekit-0.3.20+2/darwin/Classes/InAppPurchasePlugin.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98c7ccaa7a7e07ecaee89f7290edeb0b22", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/in_app_purchase_storekit-0.3.20+2/darwin/Classes/messages.g.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98d8ca7ae79d9cbb40a6b86ec5c1440420", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/in_app_purchase_storekit-0.3.20+2/darwin/Classes/messages.g.m", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98143183c5b0cba92e839dbf39d1133d20", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/in_app_purchase_storekit-0.3.20+2/darwin/Classes/Protocols/FLTMethodChannelProtocol.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98ad60c20017a5ce270ad84e965ead88c2", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/in_app_purchase_storekit-0.3.20+2/darwin/Classes/Protocols/FLTMethodChannelProtocol.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98ea341f756b663df7f5b476c2d2f23673", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/in_app_purchase_storekit-0.3.20+2/darwin/Classes/Protocols/FLTPaymentQueueHandlerProtocol.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98f224d8d6d82e4718a5f92b18e03ca386", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/in_app_purchase_storekit-0.3.20+2/darwin/Classes/Protocols/FLTPaymentQueueProtocol.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e980f369d35eaf4fd7d6dc68bea499729e8", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/in_app_purchase_storekit-0.3.20+2/darwin/Classes/Protocols/FLTPaymentQueueProtocol.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9859115f12802ef73da9e75320aed3971e", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/in_app_purchase_storekit-0.3.20+2/darwin/Classes/Protocols/FLTRequestHandlerProtocol.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e986ba24e1b04f6e46e08420e62d608f554", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/in_app_purchase_storekit-0.3.20+2/darwin/Classes/Protocols/FLTRequestHandlerProtocol.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98157ad08cbbceb228fb5fab9897bd3d77", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/in_app_purchase_storekit-0.3.20+2/darwin/Classes/Protocols/FLTTransactionCacheProtocol.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e988cebe96d2bbfbecd0161b2f7c24b00ff", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/in_app_purchase_storekit-0.3.20+2/darwin/Classes/Protocols/FLTTransactionCacheProtocol.m", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e988c32c285d8de721d6c83a2bd49bb678b", "name": "Protocols", "path": "Protocols", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98892cbc1c9dbce03295de64a6c4887a1a", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/in_app_purchase_storekit-0.3.20+2/darwin/Classes/StoreKit2/InAppPurchasePlugin+StoreKit2.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e984e882adb230515536700534db1b0c4a7", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/in_app_purchase_storekit-0.3.20+2/darwin/Classes/StoreKit2/sk2_pigeon.g.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9857c4dbf8e064457a26dd0c991f4f7b04", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/in_app_purchase_storekit-0.3.20+2/darwin/Classes/StoreKit2/StoreKit2Translators.swift", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e983ccfadb20de5eae7613d3bb7af0d5cca", "name": "StoreKit2", "path": "StoreKit2", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9879998c716761d181117db9424ca7d5ec", "name": "Classes", "path": "Classes", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e98566e515caf942fcdd23ebab6ab58ee18", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/in_app_purchase_storekit-0.3.20+2/darwin/Resources/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e989baae0a00331cc99499a0f44f3293d88", "name": "Resources", "path": "Resources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98af542afdcecc17e78d44b66d9626b59f", "name": "darwin", "path": "darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9823c4116b6ec3906bfae7568fb90c5a50", "name": "in_app_purchase_storekit", "path": "in_app_purchase_storekit", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98b04fa9d696a21665fe2de20caaa47f9c", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98bd4618bc5c3a9b0f313ea97217ca0104", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e982ea794bb60cac28ad7e4183c6f754677", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9830b61b06c8c0f016743e157ce92c8ab3", "name": "test_notifictions", "path": "test_notifictions", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98a85fbfa864cf2cba05d52e2fc847fe76", "name": "StudioProjects", "path": "StudioProjects", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e988e43713b01edf97d9a8bbe6ab134018f", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e981a582f30aea93444d19ea88269c89603", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9896220b26906531eb06c466404977f6a9", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98308b9d73d2c6cb23f7b00c12c0992c54", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98a328b16e1664b5e1d3f9f3b1d849daa5", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98c3bb139af4f6228826cd6b32cf146d04", "name": "..", "path": "../../../../../../../.pub-cache/hosted/pub.dev/in_app_purchase_storekit-0.3.20+2/darwin", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e9830c39a8b0f55dbf6f456e575f5c99f7d", "path": "../../../../../../../.pub-cache/hosted/pub.dev/in_app_purchase_storekit-0.3.20+2/darwin/in_app_purchase_storekit.podspec", "sourceTree": "<group>", "type": "file"}, {"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e98a50352263679c7a92b9c57a5838e4ca8", "path": "../../../../../../../.pub-cache/hosted/pub.dev/in_app_purchase_storekit-0.3.20+2/LICENSE", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9879f22bea6cd5552ca0e1f1f86e346c60", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e9833853504f69dd233e35213150f868ecd", "path": "in_app_purchase_storekit.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98eb999624d7773315bb8049c1949f5eb5", "path": "in_app_purchase_storekit-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e9899b368ead41771e4887bfb9fc85cdeaa", "path": "in_app_purchase_storekit-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98cf92397c6ba9da335c71afeef5bda0d7", "path": "in_app_purchase_storekit-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98f719080072fe4b497544b3c553e3e188", "path": "in_app_purchase_storekit-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e9800f063279572cee20f6f65713bdac3fc", "path": "in_app_purchase_storekit.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e985614711376e60aa59d97089a1beb0114", "path": "in_app_purchase_storekit.release.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e988ebc0e843f836f6f42907491282b8156", "path": "ResourceBundle-in_app_purchase_storekit_privacy-in_app_purchase_storekit-Info.plist", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9843099017d689bc449835aac1580e2c18", "name": "Support Files", "path": "../../../../Pods/Target Support Files/in_app_purchase_storekit", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9840c9f311cfa37a60653f97f911673104", "name": "in_app_purchase_storekit", "path": "../.symlinks/plugins/in_app_purchase_storekit/darwin", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98ed222a0fecd48545d1b8a17e58c4093a", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/open_filex-4.6.0/ios/Classes/OpenFilePlugin.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98f7073235a953b3031285b87eae46b351", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/open_filex-4.6.0/ios/Classes/OpenFilePlugin.m", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98ccda922390cb339fcc23c77be1d75b76", "name": "Classes", "path": "Classes", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9810ae76b3106db21e515ff13d64e207fd", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e987706a8c1a1692ea518a9b3ba8208cce4", "name": "open_filex", "path": "open_filex", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9872cc2f95af69afabbeb9fe9e8ce05743", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98dc71a0acedaeb4042c6898eb873bcb7a", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98bfbfd7ce7c79e80df74a0662969c09a6", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98095c604fefe8f7185080ea9071b6851c", "name": "test_notifictions", "path": "test_notifictions", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e988f51485be6ef87e0b53cf5b489249a24", "name": "StudioProjects", "path": "StudioProjects", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e986591a02104a24f9e492e8de1dfaddc7d", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9803d8eb7d12803ffb84f6ba3a213dfda4", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98cf5332549c50715f32483ff8086bd9c1", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e987e44396312484be4b2a93f61ada23d09", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9895adf06a5cba69c76d585314bf8c7379", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98bb109c5f820270432efe26e5d9c82758", "name": "..", "path": "../../../../../../../.pub-cache/hosted/pub.dev/open_filex-4.6.0/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e980bfdaebdc278c4d1b781fc642bfd7fa5", "path": "../../../../../../../.pub-cache/hosted/pub.dev/open_filex-4.6.0/LICENSE", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e98cd9b533c26a965004b5fdb70d142fc03", "path": "../../../../../../../.pub-cache/hosted/pub.dev/open_filex-4.6.0/ios/open_filex.podspec", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98e161d2eabc472f8cd3526816a3266caa", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e98c0574c06fd9cf55ea7999f5b13cdb0e1", "path": "open_filex.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9861f2843db19b5c4f12bed8cefbd4d551", "path": "open_filex-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e983681b07a03d9f9c8f8abb38a7032fddb", "path": "open_filex-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e986fcbff64d4b835c8cab5ff3d988da06c", "path": "open_filex-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9870c8f817d8ea7773813807f7762a1d68", "path": "open_filex-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e9811af2ada817664d907653891461c593a", "path": "open_filex.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98aba3824497d8d4ce4cbf98dae9c3fb8d", "path": "open_filex.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e982872409ba23bcb1f5bb0cc3ed9951c96", "name": "Support Files", "path": "../../../../Pods/Target Support Files/open_filex", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98cec0c8f2a9751c31d2c7963e7461c3f7", "name": "open_filex", "path": "../.symlinks/plugins/open_filex/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e987d2eac1c50cfac538614706208d966f7", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/path_provider_foundation-2.4.1/darwin/path_provider_foundation/Sources/path_provider_foundation/Resources/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9857aa39abae733884f4521651feb51296", "name": "Resources", "path": "Resources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e984b790c0126ad33fe338ea2a2b13723b4", "name": "path_provider_foundation", "path": "path_provider_foundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98fdb26f2c65762dfa005d0a6bfab431dc", "name": "Sources", "path": "Sources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e989a0a3fbdee3eb52db5bf6f08c4898637", "name": "path_provider_foundation", "path": "path_provider_foundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e984377c791e8f5117b0e316d4a8069b01b", "name": "darwin", "path": "darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98953ea04f3737c4fa9acb5eb8b4a94172", "name": "path_provider_foundation", "path": "path_provider_foundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98048ad3a7fb8a44a070db7d7e53737662", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98aaaca5b2c31d8305c21ec74ff530128a", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9821d9b6d1a99eca7382aea3e35ff8f7e2", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9828d65fdc2c227b5ce380cdeac73ffa29", "name": "test_notifictions", "path": "test_notifictions", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98a1392d12d734d3509ab52255e7d7ead8", "name": "StudioProjects", "path": "StudioProjects", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98432a58703803d51d2ba6e36b166c9bff", "name": "..", "path": ".", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98fcef84b7fd05b8ebbdf88d74a9ada8b6", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/path_provider_foundation-2.4.1/darwin/path_provider_foundation/Sources/path_provider_foundation/messages.g.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e986b04b7bf2aad7377f7902bc2e333b713", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/path_provider_foundation-2.4.1/darwin/path_provider_foundation/Sources/path_provider_foundation/PathProviderPlugin.swift", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98321af51ae36662a917e23843058ac476", "name": "path_provider_foundation", "path": "path_provider_foundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9852785d0e37e451dc1932d46cb55385ad", "name": "Sources", "path": "Sources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98f7c88707a9066cb8fcede23f120d4abb", "name": "path_provider_foundation", "path": "path_provider_foundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98e8e00823e00ea2cddf9c5285723d2975", "name": "darwin", "path": "darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9877b55158ee19944d30498550a86f5239", "name": "path_provider_foundation", "path": "path_provider_foundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98ec67d76050b60b14605adfad37f5d77c", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9842cabaadf5b96ac51c0d042f5f363198", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e987ded19ccc21cab5fda0b6801a154c13b", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98eb29f99cf1a435ef78d3af666d82cfb3", "name": "test_notifictions", "path": "test_notifictions", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98dbf5571c3a6940a0a378c6b44d9353a7", "name": "StudioProjects", "path": "StudioProjects", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9800c52d703d3f61f6e949d69b66d27cfe", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9816f59de94ec174f1a6cb314bd10fcb81", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98fae1eb42e2687f03d12bb5b7c90b6776", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98fabbb5f0fd28e8335597dccbec4efd4e", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9877dc2d8401cb39356094413563f0a66b", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98c70ffdce14480391c548a7ed214654a0", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e982c8eb0b10bcb27141a7a492a02ec475d", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9898ddfd9f511071362223bcdc7a5564e0", "name": "..", "path": "../../../../../../../.pub-cache/hosted/pub.dev/path_provider_foundation-2.4.1/darwin/path_provider_foundation/Sources", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e98599a4cb5c133db3ea51bc9a2be2302ab", "path": "../../../../../../../.pub-cache/hosted/pub.dev/path_provider_foundation-2.4.1/LICENSE", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e98f67efede679d2825fdffd0f79c18c1da", "path": "../../../../../../../.pub-cache/hosted/pub.dev/path_provider_foundation-2.4.1/darwin/path_provider_foundation.podspec", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98663dd1150765dfc9c75502d1866634ee", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e9875883b17f1dde3273a2406f54df107e6", "path": "path_provider_foundation.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9847bbca9e6cddbfd83758aecbfad487a3", "path": "path_provider_foundation-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98745dfa78f7c3b0a50c53c49d8db103b3", "path": "path_provider_foundation-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98e86b11130bb0f01249a6f86519e186e5", "path": "path_provider_foundation-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e988f8cfcf19ea1990326567dab3e460243", "path": "path_provider_foundation-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e988ccee6f108fd1e0590c3b209fce78efb", "path": "path_provider_foundation.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98abb99d77ac33a2ebfd722d8a22edd8d4", "path": "path_provider_foundation.release.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e989472ee9c2e9e0b0891adb05a654ee190", "path": "ResourceBundle-path_provider_foundation_privacy-path_provider_foundation-Info.plist", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98206fe77583fac22963abb28f8a8d9240", "name": "Support Files", "path": "../../../../Pods/Target Support Files/path_provider_foundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e984168fa8544e1851fbb3adcd28103b44a", "name": "path_provider_foundation", "path": "../.symlinks/plugins/path_provider_foundation/darwin", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e983c1d689b21bc847929c9d458fbe5036b", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.5/ios/Classes/PermissionHandlerEnums.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98ebbb16b55bc5af0a12adc5b762a2ca04", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.5/ios/Classes/PermissionHandlerPlugin.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98f316c7025a4c93b52e3ae185240ac535", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.5/ios/Classes/PermissionHandlerPlugin.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98f086990d52543e5b0b0b8b2b8038c2f7", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.5/ios/Classes/PermissionManager.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98583ec42f07b36ebc19c7002b95c839d4", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.5/ios/Classes/PermissionManager.m", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e982d12280252702588d7cd7ebb34188947", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.5/ios/Classes/strategies/AppTrackingTransparencyPermissionStrategy.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98826a91a14b2a494604b4345a727db301", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.5/ios/Classes/strategies/AppTrackingTransparencyPermissionStrategy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e985796ceaa60e5401fb107b71373e81eee", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.5/ios/Classes/strategies/AssistantPermissionStrategy.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98584c9a543fb325db0db2e1426e117d77", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.5/ios/Classes/strategies/AssistantPermissionStrategy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9825294332c0e6e682dfd9e6ec6e79687a", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.5/ios/Classes/strategies/AudioVideoPermissionStrategy.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e986899bd9b3814324b9caf73badc14ae6c", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.5/ios/Classes/strategies/AudioVideoPermissionStrategy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9810671342612597a0338142e7cc137a69", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.5/ios/Classes/strategies/BackgroundRefreshStrategy.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98a231cffd0fa1d33e89a49af92f3dacb9", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.5/ios/Classes/strategies/BackgroundRefreshStrategy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98d8d3a3e4827c83e5027faabaa1078cf6", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.5/ios/Classes/strategies/BluetoothPermissionStrategy.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9853b70620299487a92a55b51ab6b49a81", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.5/ios/Classes/strategies/BluetoothPermissionStrategy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9869ec23e153faa4a637b6dfc52c891ed2", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.5/ios/Classes/strategies/ContactPermissionStrategy.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98b650495981bacfd92d3753a26f001b95", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.5/ios/Classes/strategies/ContactPermissionStrategy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9888f2f981e93c01bf88f7806850d3f873", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.5/ios/Classes/strategies/CriticalAlertsPermissionStrategy.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9855816479e8d901955530483bfd41e239", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.5/ios/Classes/strategies/CriticalAlertsPermissionStrategy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98ff20ea98459e018ef52c92c8b5e9da78", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.5/ios/Classes/strategies/EventPermissionStrategy.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98ec8fbb80b50642f29d636045882aeb1a", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.5/ios/Classes/strategies/EventPermissionStrategy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9874fbfc492b344808f1e148d036b517d2", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.5/ios/Classes/strategies/LocationPermissionStrategy.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98d595f4463c1882edd253952f764e4185", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.5/ios/Classes/strategies/LocationPermissionStrategy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e985ecd3b896a16ae293c3ef74bbb3ad48a", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.5/ios/Classes/strategies/MediaLibraryPermissionStrategy.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98be377ba7d52472eded2e9951b6259879", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.5/ios/Classes/strategies/MediaLibraryPermissionStrategy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98879d4bb6efef1691ac0a3d50dffcf678", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.5/ios/Classes/strategies/NotificationPermissionStrategy.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e982548d3384ab13ffbfe5c0291a1d8a1fe", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.5/ios/Classes/strategies/NotificationPermissionStrategy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9829dd9a4a2109c88dbef222d43f07f98c", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.5/ios/Classes/strategies/PermissionStrategy.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98dd9526f1088326876a64b1f239e9537b", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.5/ios/Classes/strategies/PhonePermissionStrategy.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e981248c8a7a1c7a65f3f319b6517ae8b64", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.5/ios/Classes/strategies/PhonePermissionStrategy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e980f5f0d443376f16ab173962e812043c6", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.5/ios/Classes/strategies/PhotoPermissionStrategy.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98fe898869a2a2c6e2bbbd09a6920fd302", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.5/ios/Classes/strategies/PhotoPermissionStrategy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9804b2b3eb1cfbeeaae0d7ba6057d96cd7", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.5/ios/Classes/strategies/SensorPermissionStrategy.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98fc0290538ab83b1e228597b314a2207a", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.5/ios/Classes/strategies/SensorPermissionStrategy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e986790efccb6649be1929f81adaafd7609", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.5/ios/Classes/strategies/SpeechPermissionStrategy.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e982f98fb639c3c431a96e7e7c5dabeb46f", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.5/ios/Classes/strategies/SpeechPermissionStrategy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e984c6aed266cace1a18be4dbc2cc711fe9", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.5/ios/Classes/strategies/StoragePermissionStrategy.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98ecfdf25fd84aaf6edfe0e6570cd37d6e", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.5/ios/Classes/strategies/StoragePermissionStrategy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9851e1aa88c7b6735a647fbb5f2844daca", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.5/ios/Classes/strategies/UnknownPermissionStrategy.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98210aaf41765f930b1d80e1c3e91a70ef", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.5/ios/Classes/strategies/UnknownPermissionStrategy.m", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9893beccad68b650b76d06cf7122181d25", "name": "strategies", "path": "strategies", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98edf1bcd5bbd9d28907f90bbf090207d2", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.5/ios/Classes/util/Codec.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98fdfb22c482971178fb3da7cee0aeb76a", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.5/ios/Classes/util/Codec.m", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e980c3a94c00678b2e969024358e168f89f", "name": "util", "path": "util", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9862bf96d81af831db49389605c34bad98", "name": "Classes", "path": "Classes", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e981ceac25977a1eb5a8bf0e8e9a03cc616", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.5/ios/Resources/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9888ad1875a6a0f2fbddce66a286b6c09d", "name": "Resources", "path": "Resources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98fa7847b1b4aa1e4d527d8840153115e1", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98ab276c8534f70f64ef5fcae3e58ed99e", "name": "permission_handler_apple", "path": "permission_handler_apple", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98b28415aa8f2ef4ab476b153d4857dd0b", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9861c1a66d25ce8c33709402ab4ced7ab7", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e983a24b2c66553a087dfa5fc3980a7ed3b", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98b06ad08ddd2a5420f4e26ec19fae338b", "name": "test_notifictions", "path": "test_notifictions", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e989c0757ab88800c62feba4c35d047f393", "name": "StudioProjects", "path": "StudioProjects", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98b5c380eb74cd29f3e07aa51c97c36b8c", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9847c5b84b61a066dbef712668f0f6ec01", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9828a9b12d0d7b8e0184756f93e4ef9c2b", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98ec77010c20688422e838c4d2d8b35f9d", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98c01482a0baa8501adb579c9debe7ce17", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e986ba7ee361af99d87d615a50070f609c5", "name": "..", "path": "../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.5/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e985d5bb1e0007a4ec6170a524fd99c926c", "path": "../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.5/LICENSE", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e98c31fd377212c5fdf6a8a7d094b4a1291", "path": "../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.5/ios/permission_handler_apple.podspec", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98ca206720fd8cc7569d3cd7eaa7f27a58", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e9865b314127e69eee2b7449ff438fae4d8", "path": "permission_handler_apple.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e984d8aff2d384d277b28541a0fec3bf47f", "path": "permission_handler_apple-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98f4c746b1ff5f4bb53940923e73c02c4b", "path": "permission_handler_apple-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98e1106e46cb4b27efa31d0177a2209598", "path": "permission_handler_apple-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e986db34133c8ad15e53cb40f57bfd0c0d9", "path": "permission_handler_apple-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98646e27d1cddb8a28840403e105120f99", "path": "permission_handler_apple.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98e3e3a98355c126810a4089d7f52cd69a", "path": "permission_handler_apple.release.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e982e13ae00e34298a5c93ed25b161819c7", "path": "ResourceBundle-permission_handler_apple_privacy-permission_handler_apple-Info.plist", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e982560b04cccdbf9cd22501ce80bc3bc2a", "name": "Support Files", "path": "../../../../Pods/Target Support Files/permission_handler_apple", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e987faac6e50f50ed964df2b73afe82eab9", "name": "permission_handler_apple", "path": "../.symlinks/plugins/permission_handler_apple/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e980a42227f3e1e22d58eec47526c755cfd", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/share_plus-7.2.2/ios/Classes/FPPSharePlusPlugin.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9816f77ee063aa8218a89f64bd294af269", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/share_plus-7.2.2/ios/Classes/FPPSharePlusPlugin.m", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9882baab4eb302251f33268afafd711517", "name": "Classes", "path": "Classes", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e984c943bd0207ebdb5ea969d32ecad2782", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e983ef4a881802b7d52180f1941b9a476fd", "name": "share_plus", "path": "share_plus", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e985dea1d3de6852f798addc01d20dec8ae", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98e8ad0a0a5de428be272a118478021a2b", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e986606fcda08078342533289e5a3e9c882", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98980ecc9ae309b8d957386fc8c9cbaf17", "name": "test_notifictions", "path": "test_notifictions", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9827e1dc7d656527968ac4e2fe7c9ecd12", "name": "StudioProjects", "path": "StudioProjects", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9812c9d93ebc8a20f5588f93c2bacb462b", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e980d3995912a836536dbcfb9e1943dbaec", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98d06db1751829fec0d1b960eadd0f2fc9", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98a3151bb1c27439b2c3dea4f0772401d6", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9884622026c9250b34e89a9b92a35d939f", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e982d5b1e5da85bc7173994b281f0240488", "name": "..", "path": "../../../../../../../.pub-cache/hosted/pub.dev/share_plus-7.2.2/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e98119bc1046e74dc52abc6c2bfcd412691", "path": "../../../../../../../.pub-cache/hosted/pub.dev/share_plus-7.2.2/LICENSE", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e98cb22019bf3331e6d2a8e75693fd8d82c", "path": "../../../../../../../.pub-cache/hosted/pub.dev/share_plus-7.2.2/ios/share_plus.podspec", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e983b8b71d5dcbd1c9fb2c1609b5b17b55b", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e98f8fdac35161e4c9ff9cae89c45d56a5a", "path": "share_plus.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9887dd6964cae81ad9e55877663f692036", "path": "share_plus-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e984df0b2a3132a43ae74c0f53204e50b71", "path": "share_plus-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e987f9be06986e803a05b29d6393b49a5cd", "path": "share_plus-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e988e8164b29a761be381d98fbb5cdd6c86", "path": "share_plus-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e9814c4c1d4a96ba8178889db7b679cc991", "path": "share_plus.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e9842c449bfc82c087f41b714ca94653c08", "path": "share_plus.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98377f422ef8fdadc2d9db2d63923ee56b", "name": "Support Files", "path": "../../../../Pods/Target Support Files/share_plus", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98da4afd1e4c1429969ff6fc521108af91", "name": "share_plus", "path": "../.symlinks/plugins/share_plus/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e981d3104dfc81871f8d4710192cdf72c2b", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/shared_preferences_foundation-2.5.4/darwin/shared_preferences_foundation/Sources/shared_preferences_foundation/Resources/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9876309247f2547972d4672e0742ecfa2a", "name": "Resources", "path": "Resources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e989959b2421412a1140ad09ae594dc12b7", "name": "shared_preferences_foundation", "path": "shared_preferences_foundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98c5a2f3b50c62a93476797eef883fcc76", "name": "Sources", "path": "Sources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e985cd52441d25fc4a4c82b37ec56537ded", "name": "shared_preferences_foundation", "path": "shared_preferences_foundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98a0174011808902c575d26adee1c28022", "name": "darwin", "path": "darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98450a6e05a9d2b210fb209e81b165956f", "name": "shared_preferences_foundation", "path": "shared_preferences_foundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98044cdc61a06fafa8684639b779e915e9", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98a08a7b4d6f7effeaaa1c3be668855d1e", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98e50c4ee2b90a7d4c0d3e8d442046a08b", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e984bcc36b12685959fb0aa2d1fa4122744", "name": "test_notifictions", "path": "test_notifictions", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e980e199c9f69508c535daff76191bee21e", "name": "StudioProjects", "path": "StudioProjects", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98ed73653e54bad5513faaa9c6cd8151f2", "name": "..", "path": ".", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e983cc372276c49ef7e56c2f4da74d63e97", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/shared_preferences_foundation-2.5.4/darwin/shared_preferences_foundation/Sources/shared_preferences_foundation/messages.g.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98dc12136327b46fbad0393a79cbfa1725", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/shared_preferences_foundation-2.5.4/darwin/shared_preferences_foundation/Sources/shared_preferences_foundation/SharedPreferencesPlugin.swift", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98fd1a63a2f47698a223b913ddb57245fc", "name": "shared_preferences_foundation", "path": "shared_preferences_foundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e981b45963ec1c337c48fb6a983f4706e52", "name": "Sources", "path": "Sources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9883516353d9ac85ff8b561d84a0fc02e0", "name": "shared_preferences_foundation", "path": "shared_preferences_foundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e989234a82fc9bf11b9df1edeb6eaf5ab1e", "name": "darwin", "path": "darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e988f9671c083bcca8690bb5967d10e4386", "name": "shared_preferences_foundation", "path": "shared_preferences_foundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9851caedc5910fc10cd763ee8ba307fdd3", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e982375ea7450cf2f5bd69bd4300f2638c2", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98284985ca76e530ee4b79e6c0e4f443ed", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98adb3204c321d6a774b1cc7824b11179b", "name": "test_notifictions", "path": "test_notifictions", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98c3c36237aa63d9f6732468ac831b4ea1", "name": "StudioProjects", "path": "StudioProjects", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98bed65f47213256ab6edd892beeadba2d", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e980d1f34848fdc60067777dc018156e800", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9865decdf9f4836521652d30219a75ddfb", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9897f6018df92c2b9c895f62b68a199e50", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98454cdde3d26926fa6aa270c00950fcab", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98fa65ceec70fedc5089a86f2063df4e84", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98e6f44cfc08cb048cb440b7a5cae757e0", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9845539dac11717e68805fd292767ffc83", "name": "..", "path": "../../../../../../../.pub-cache/hosted/pub.dev/shared_preferences_foundation-2.5.4/darwin/shared_preferences_foundation/Sources", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e98c450f5634871d4cdbf8e8628bc718ca5", "path": "../../../../../../../.pub-cache/hosted/pub.dev/shared_preferences_foundation-2.5.4/LICENSE", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e987de207474c435e7857324ed41694fb97", "path": "../../../../../../../.pub-cache/hosted/pub.dev/shared_preferences_foundation-2.5.4/darwin/shared_preferences_foundation.podspec", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98d32fc31eee604c00aaa3e125c15743b8", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e9831b1df0f7c36bd156c86c8db4c8c3122", "path": "ResourceBundle-shared_preferences_foundation_privacy-shared_preferences_foundation-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e9870558770423f1bd3b4efaa3af2cda2e2", "path": "shared_preferences_foundation.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e980886c7c86fea356ba1dcec5f2e10c110", "path": "shared_preferences_foundation-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e9861ce551d06b9ccb52ee2c2213aa610f9", "path": "shared_preferences_foundation-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98d192bfb42f002d5bf44a29db9b11a94c", "path": "shared_preferences_foundation-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e986db5e2063a255a9ed7b63ec578e04dbd", "path": "shared_preferences_foundation-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e982d2850ae6d0edadc6aba2da659fdaec0", "path": "shared_preferences_foundation.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e984c8842a32903376133f982abff4137ec", "path": "shared_preferences_foundation.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98133d0776b50531e67c0fef769120eabe", "name": "Support Files", "path": "../../../../Pods/Target Support Files/shared_preferences_foundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e982a8761445c45cbc86a85bcd85ca7cc0d", "name": "shared_preferences_foundation", "path": "../.symlinks/plugins/shared_preferences_foundation/darwin", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e985f4470e02e5e6ddb928a690e302ec12e", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.1/darwin/sqflite_darwin/Sources/sqflite_darwin/Resources/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98405e2d3b7781f507bb002c088ca881bd", "name": "Resources", "path": "Resources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9834c90a709e501a6ddffa999073030594", "name": "sqflite_darwin", "path": "sqflite_darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98fada166fba5517c0837b3dc55e42c65c", "name": "Sources", "path": "Sources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98c7af6a489df454c50b8aa3fdfbd838a7", "name": "sqflite_darwin", "path": "sqflite_darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98b899fc9ec513b5f60f0761787a94f8f9", "name": "darwin", "path": "darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98529bafd65a5f93874dd34c7683812e52", "name": "sqflite_darwin", "path": "sqflite_darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e988aafdd3bc4b39b775f8e71dc9fd28b3b", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9830637fa173431a8b59d3eb00f7608b18", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98a9b56718a855e2e52e517a9b5616227c", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98e42f8ab1bbdc598a50843fe5ec5987dd", "name": "test_notifictions", "path": "test_notifictions", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98f0161d92ecf011c9dc2cf68b6a840eb7", "name": "StudioProjects", "path": "StudioProjects", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98e665722925234bb60cd26de1b7599fd6", "name": "..", "path": ".", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e983439b38d224399cb2a4733ea19fdc862", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.1/darwin/sqflite_darwin/Sources/sqflite_darwin/SqfliteCursor.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e987b4bccb33abfb2d29dcaed584c0ca005", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.1/darwin/sqflite_darwin/Sources/sqflite_darwin/SqfliteCursor.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98f316212e0d40780877fe13bfcc679f4e", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.1/darwin/sqflite_darwin/Sources/sqflite_darwin/SqfliteDarwinDatabase.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e980b35d0539ba7e700bd2a28d067a52996", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.1/darwin/sqflite_darwin/Sources/sqflite_darwin/SqfliteDarwinDatabase.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98b94dfea424e4355adff4a02defe74ac0", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.1/darwin/sqflite_darwin/Sources/sqflite_darwin/SqfliteDarwinDatabaseAdditions.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98556f91e0b565de23b4cf6a1f44f1837b", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.1/darwin/sqflite_darwin/Sources/sqflite_darwin/SqfliteDarwinDatabaseAdditions.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98fe7882682d6a3291ef73de4b355499b4", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.1/darwin/sqflite_darwin/Sources/sqflite_darwin/SqfliteDarwinDatabaseQueue.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e987dc3de11cf58183aca05f8f520e25a7f", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.1/darwin/sqflite_darwin/Sources/sqflite_darwin/SqfliteDarwinDatabaseQueue.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e988c3b4847499352b5bd5f7dfe7e1147ff", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.1/darwin/sqflite_darwin/Sources/sqflite_darwin/SqfliteDarwinDB.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e983a5864602c7ce22bc770d8e52c350ba5", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.1/darwin/sqflite_darwin/Sources/sqflite_darwin/SqfliteDarwinImport.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e987d38268bf5a549aa157e3348b7cc901c", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.1/darwin/sqflite_darwin/Sources/sqflite_darwin/SqfliteDarwinResultSet.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98e4965790fa7c19eda51945ca8d5d27a5", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.1/darwin/sqflite_darwin/Sources/sqflite_darwin/SqfliteDarwinResultSet.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e981daeeb34f0fcd6f9d2c1bf99ba54f2f3", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.1/darwin/sqflite_darwin/Sources/sqflite_darwin/SqfliteDatabase.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98b9d53e7ea706f92d3e40482ff1240766", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.1/darwin/sqflite_darwin/Sources/sqflite_darwin/SqfliteDatabase.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98eaa5c1ecb77a18cc9a6fe2fe0185834c", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.1/darwin/sqflite_darwin/Sources/sqflite_darwin/SqfliteImport.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e989c6ffc42a45ccbdebe18d51409aa732d", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.1/darwin/sqflite_darwin/Sources/sqflite_darwin/SqfliteOperation.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98582d29610bf4c13c8492bc21ae744eee", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.1/darwin/sqflite_darwin/Sources/sqflite_darwin/SqfliteOperation.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98d68d263970c2d27f229f201a9f0eb01e", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.1/darwin/sqflite_darwin/Sources/sqflite_darwin/SqflitePlugin.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e989f3ca59ec2f449bb226559139dd0b927", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.1/darwin/sqflite_darwin/Sources/sqflite_darwin/SqflitePlugin.m", "sourceTree": "<group>", "type": "file"}, {"children": [{"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9830098aded95ac7fd81c73ad1fbc4f6d7", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.1/darwin/sqflite_darwin/Sources/sqflite_darwin/include/sqflite_darwin/SqfliteImportPublic.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98eacf43a4e5a5f3e5cc65c37c1889f4ba", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.1/darwin/sqflite_darwin/Sources/sqflite_darwin/include/sqflite_darwin/SqflitePluginPublic.h", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9850b485c54cab8db7c9596a39a6f87632", "name": "sqflite_darwin", "path": "sqflite_darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9859d3a3a43586a9297b4c509b7dbc5e3f", "name": "include", "path": "include", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98b7193923801d394441aecd9aa5f6d19b", "name": "sqflite_darwin", "path": "sqflite_darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9815d005f619907f7ac2c9fe605fdd5f32", "name": "Sources", "path": "Sources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e980639d995cb0de4f3c5291b4ed5afd73c", "name": "sqflite_darwin", "path": "sqflite_darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98217b21aee67763a407e0903583ecef6c", "name": "darwin", "path": "darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98ef2787cca4a640ae9e015c0564f646a6", "name": "sqflite_darwin", "path": "sqflite_darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98caae9db8b971b515c9d65024121d3989", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98cacb6717f04659985dcb54358449e96a", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e985cb5920ec6f9e878e2f8564fdb351bfc", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e983b5b8e5491ffdcaacb12f4e9ab5d0320", "name": "test_notifictions", "path": "test_notifictions", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98ceec810cb9fa915275cb2a6cddfbf7ff", "name": "StudioProjects", "path": "StudioProjects", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e981c0048add700751e23773823e8176e68", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98f14b42b5ab47a6f35bebf383e1c6c1e9", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9804b9acdec4290cdfd22468eaffbb173b", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98a1f3ec6a45ba10322b85466cd00eff85", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9853846e2373f746441593325a9c1bd884", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98ecb9f13e18ca4e5b47000b735ec299c1", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98e9ea0ff7abed7f91feafd179565a5752", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e981b0b41a1356d69b4cb29945f2be58978", "name": "..", "path": "../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.1/darwin/sqflite_darwin/Sources", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e9873ee9f894770c63b2167cbc8990c58b9", "path": "../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.1/LICENSE", "sourceTree": "<group>", "type": "file"}, {"fileType": "net.daringfireball.markdown", "guid": "bfdfe7dc352907fc980b868725387e98e4541fa35eba40f581e467b9d18fbfe1", "path": "../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.1/darwin/README.md", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e98d743406744263d1cfc72188886e390ad", "path": "../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.1/darwin/sqflite_darwin.podspec", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9860b2da954220322fcf7690db070e2ec9", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98616ea494d36a225854f02ec36c3958ac", "path": "ResourceBundle-sqflite_darwin_privacy-sqflite_darwin-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e98b39df2bed151cc528e7f504ac99e57b6", "path": "sqflite_darwin.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e984ce47bfbfbe8e7c016a752e756c252e9", "path": "sqflite_darwin-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e9843e92fce0d86cea1e211bc35ad348f4e", "path": "sqflite_darwin-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e985bf0f57db7674ba1b6f6f6a3d668b454", "path": "sqflite_darwin-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98cefd81ef0d3561bcce020c5d9b8f9501", "path": "sqflite_darwin-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98ea00c774b5c5c18f64dee16d1e45fb97", "path": "sqflite_darwin.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e980097ac257a13ef015769f8cd259aecef", "path": "sqflite_darwin.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98a83a7832b3fe9952247cab89a7516164", "name": "Support Files", "path": "../../../../Pods/Target Support Files/sqflite_darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e989a8915db0039a919a842fa3a0ad05b56", "name": "sqflite_darwin", "path": "../.symlinks/plugins/sqflite_darwin/darwin", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e9891b8efef34ebe1e7b3d5cb770e992f05", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/url_launcher_ios-6.3.2/ios/url_launcher_ios/Sources/url_launcher_ios/Resources/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e980c42f48989c9f9ba106ce21545a6122a", "name": "Resources", "path": "Resources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98689165f2d3492e3559c4760ea01ccb4b", "name": "url_launcher_ios", "path": "url_launcher_ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9809e6ba2c57779941e551922fd53ef129", "name": "Sources", "path": "Sources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e986372b55ebdaf4f8903ebba70c5bb8d06", "name": "url_launcher_ios", "path": "url_launcher_ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98628947d27ed88e5644803f4003f17260", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e989e432021f590821b7c7b1ca4e71fb48c", "name": "url_launcher_ios", "path": "url_launcher_ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e981eae14d113b2642e34bfbad021d0ecef", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9871b36d006547036ef58da6788a6c673a", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98a0dfeba4e13f1c490d620ea49e084e01", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98f9427643c6d7783dce76e6662e2884ec", "name": "test_notifictions", "path": "test_notifictions", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98675edebb499d12a09e0f468400fc2da7", "name": "StudioProjects", "path": "StudioProjects", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e989ec51ca6fa7929b5a6d161a161cd8cc4", "name": "..", "path": ".", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98f800dc01ab3d809691d123ba773b2362", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/url_launcher_ios-6.3.2/ios/url_launcher_ios/Sources/url_launcher_ios/Launcher.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e987f061327a5db2eb245786c256d9d17da", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/url_launcher_ios-6.3.2/ios/url_launcher_ios/Sources/url_launcher_ios/messages.g.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e985228c936c29283b08f62d9cabcbe7d80", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/url_launcher_ios-6.3.2/ios/url_launcher_ios/Sources/url_launcher_ios/URLLauncherPlugin.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e982fdb167bc2cf389a3f05737dfdb913f7", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/url_launcher_ios-6.3.2/ios/url_launcher_ios/Sources/url_launcher_ios/URLLaunchSession.swift", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9855121eacd6ac85461c78a5d6bdd9a5b0", "name": "url_launcher_ios", "path": "url_launcher_ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98f3ca337442810391906901209663dcca", "name": "Sources", "path": "Sources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e983343e7e43db73b53135541d70da4f643", "name": "url_launcher_ios", "path": "url_launcher_ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98a68a40f56a2286f2e9a8ff1fecc21a7a", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98f93cb2f57a52f343eb69fcb5240196b2", "name": "url_launcher_ios", "path": "url_launcher_ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9868e927ab8a3609c3b819243b0880ae24", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98b68297d4c140e2fa9d2ece27fd5a1ce5", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e985fcab5cb8bf4ec9106238ffd31f93f6c", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98d7ecb85ee0a0b471e639f9b6e2bdf07d", "name": "test_notifictions", "path": "test_notifictions", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9862aef5a560421c8c5063bdad40ec551e", "name": "StudioProjects", "path": "StudioProjects", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98b22cccf8eead4f80f56b9214142f9b14", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98c5a464eeb276d4807f3a1d311884ae82", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98f251a1b3e9f4a3cf1bcf0bd95c00a0b4", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9860dfb154b968a49998ab308d77c57179", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98ea5f8750b309ab902cd3339846ee9caa", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9883ce50dca47396e7f48c83ba9aacc444", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98d439a0d015e36d8ba11cf1de694a6e6e", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98bebe56d2558986778e53f4f8af82b2ec", "name": "..", "path": "../../../../../../../.pub-cache/hosted/pub.dev/url_launcher_ios-6.3.2/ios/url_launcher_ios/Sources", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e985fb58f6bee84248e12ab011fb240f151", "path": "../../../../../../../.pub-cache/hosted/pub.dev/url_launcher_ios-6.3.2/LICENSE", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e98a92dde513d513d6d167a59890708bff7", "path": "../../../../../../../.pub-cache/hosted/pub.dev/url_launcher_ios-6.3.2/ios/url_launcher_ios.podspec", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98883b6101c60a4c94005cd01890d810e7", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e9877f244a06fa4f670344c9a23e973c3b9", "path": "ResourceBundle-url_launcher_ios_privacy-url_launcher_ios-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e98b85ca53ee0227b568bbc73fa5c50cb4c", "path": "url_launcher_ios.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9832ba10ba8f745e0bf0b09523003db398", "path": "url_launcher_ios-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e981d6a647400d18ce5e3e21d3a97746356", "path": "url_launcher_ios-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98e742215f7da5e234335764340c09c85b", "path": "url_launcher_ios-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e981d13e18372c64ef1e91aa28dba0028b5", "path": "url_launcher_ios-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98192a2c41a4a1ed4d527f8a42ef93cb85", "path": "url_launcher_ios.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e981fb910f14f481d3b436c26dae430632b", "path": "url_launcher_ios.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9895d1c96de966b6731a7e1ba20bac7e6f", "name": "Support Files", "path": "../../../../Pods/Target Support Files/url_launcher_ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e982bf8d6889c492ae8cf7554ab8f32815b", "name": "url_launcher_ios", "path": "../.symlinks/plugins/url_launcher_ios/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e984c8a88f878790133ec1e596d22673eb6", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.16.3/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/Resources/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9802ee42ac65bcfa0e86cff8a2ffe377e4", "name": "Resources", "path": "Resources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98a0b1105ad91b1dad0872f1d3ec7a68aa", "name": "webview_flutter_wkwebview", "path": "webview_flutter_wkwebview", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e983407417be329b240c5a359f77dafe8cf", "name": "Sources", "path": "Sources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98d24a27a107d761f7aff7b305a7b1b8c7", "name": "webview_flutter_wkwebview", "path": "webview_flutter_wkwebview", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e982c0e2a95b44c1b77e3369a8bbf60caaf", "name": "darwin", "path": "darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9814169a3b3569ea2730bb521c681c2257", "name": "webview_flutter_wkwebview", "path": "webview_flutter_wkwebview", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98d47a109139c8c10ce2fd8d16bc11cb99", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98cb01e7d5ceb06c7321edcfeeb28ac8ee", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e985e0f2cea95350939d53801ce562cc957", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e984568747adf72ec18abb19b7b497dbec8", "name": "test_notifictions", "path": "test_notifictions", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9812026fdd15d004dabe6c9cc05c7b8857", "name": "StudioProjects", "path": "StudioProjects", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98a45f98fb6a339ec1c4e12cdaf61355a9", "name": "..", "path": ".", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9893a26beb4b4dcbd92b6cfd8f1c9889d2", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.16.3/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/FLTWebViewFlutterPlugin.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9881d0058a5fd8ca521153b455669b732c", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.16.3/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/FWFDataConverters.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e987c4482082aca75830f75cf78d34d0556", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.16.3/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/FWFGeneratedWebKitApis.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98a4676fca3fe22db698ec62555efad7ae", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.16.3/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/FWFHTTPCookieStoreHostApi.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98eb50fe7c31487a63b31f937bd433d98f", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.16.3/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/FWFInstanceManager.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98e33d1e50b2f2db1f95e026994c895bc1", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.16.3/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/FWFNavigationDelegateHostApi.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98c00523e69e8b57b54de1c4b151f39856", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.16.3/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/FWFObjectHostApi.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e989286074969d12edce8e54dcedbcf58e7", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.16.3/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/FWFPreferencesHostApi.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e989e9385f7c4e5ab4a5e95c16c2cdbabc7", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.16.3/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/FWFScriptMessageHandlerHostApi.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9858d7f946ab4b5ca216cf705d4ca014b2", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.16.3/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/FWFScrollViewDelegateHostApi.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98663c458b65827884e995dd85f18e7534", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.16.3/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/FWFScrollViewHostApi.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98ba8c1504bab6d02c35f5966989e1291b", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.16.3/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/FWFUIDelegateHostApi.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98e3dae715a77c4668bce9a79032359449", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.16.3/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/FWFUIViewHostApi.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e987223876440f7d007d8c85cb18422184e", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.16.3/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/FWFURLAuthenticationChallengeHostApi.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e987fa974b35ef5ae144bcfdf8178adf807", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.16.3/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/FWFURLCredentialHostApi.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98e295ede6653169b90f5a4f1a44ba9adf", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.16.3/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/FWFURLHostApi.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9854ebc1e2231a64bdfea729783fe16fbf", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.16.3/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/FWFURLProtectionSpaceHostApi.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98a589ddcfbbe4d3d75185dcdd57be34b2", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.16.3/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/FWFUserContentControllerHostApi.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e988f599f6c084cf16cb3fe2de8b3120100", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.16.3/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/FWFWebsiteDataStoreHostApi.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98648a2b4e4c65004fa3c3d962fe8ce7e9", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.16.3/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/FWFWebViewConfigurationHostApi.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9818748eac770d2021996b851e276b8ac4", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.16.3/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/FWFWebViewFlutterWKWebViewExternalAPI.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98ac93755dc17a338cb5c90e34eff96c10", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.16.3/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/FWFWebViewHostApi.m", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e985cbcc303bb693f36dc386de878886fcc", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.16.3/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/include/webview-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98fdb11844d33cf1609beb59b246c08f40", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.16.3/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/include/webview_flutter_wkwebview/FLTWebViewFlutterPlugin.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98141bef8b7d67740cc724ff922744d23c", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.16.3/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/include/webview_flutter_wkwebview/FWFDataConverters.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98879de2b7a7541afcccfd058d938dd5ee", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.16.3/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/include/webview_flutter_wkwebview/FWFGeneratedWebKitApis.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9841d13afb9ceb76f3807d52c5bad6876e", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.16.3/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/include/webview_flutter_wkwebview/FWFHTTPCookieStoreHostApi.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98c58ab6ee22919052b8186fac681c7abc", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.16.3/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/include/webview_flutter_wkwebview/FWFInstanceManager.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9800b2f88f6ebe95285506d36399341bb4", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.16.3/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/include/webview_flutter_wkwebview/FWFInstanceManager_Test.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e982a049c7947c22278b0b365cd1c3804dd", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.16.3/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/include/webview_flutter_wkwebview/FWFNavigationDelegateHostApi.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98be4dec9c9b4da69bd27b612bc776f714", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.16.3/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/include/webview_flutter_wkwebview/FWFObjectHostApi.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98f347b0ee767264f24e8f4d0b0d74d5cd", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.16.3/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/include/webview_flutter_wkwebview/FWFPreferencesHostApi.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98bb9dc786ad86f5c129c7b3b5aa1c9d67", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.16.3/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/include/webview_flutter_wkwebview/FWFScriptMessageHandlerHostApi.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98cdbaa6f92acb42183149154b3c777c2e", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.16.3/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/include/webview_flutter_wkwebview/FWFScrollViewDelegateHostApi.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98f3a45322825bd68bcd7477f3f8997c22", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.16.3/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/include/webview_flutter_wkwebview/FWFScrollViewHostApi.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e987ff7770ecb84d0cf3eff4028d9871947", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.16.3/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/include/webview_flutter_wkwebview/FWFUIDelegateHostApi.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98f850686c11e3d88ac6c4496288695105", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.16.3/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/include/webview_flutter_wkwebview/FWFUIViewHostApi.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9819b49827eb7be08eb9a82e4bd0e228f3", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.16.3/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/include/webview_flutter_wkwebview/FWFURLAuthenticationChallengeHostApi.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98db456710d10f03009864dee0ae69edef", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.16.3/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/include/webview_flutter_wkwebview/FWFURLCredentialHostApi.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e984c354922981f37968fcd779da6a481f7", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.16.3/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/include/webview_flutter_wkwebview/FWFURLHostApi.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98f6916f6851581474936dd83c302d9050", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.16.3/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/include/webview_flutter_wkwebview/FWFURLProtectionSpaceHostApi.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98a5b754f11b3feb9e13d67e94ba8df36f", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.16.3/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/include/webview_flutter_wkwebview/FWFUserContentControllerHostApi.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e980e59f35c3461f3455a98de8bbdc0ddfa", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.16.3/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/include/webview_flutter_wkwebview/FWFWebsiteDataStoreHostApi.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98dcbd6dfd305c3b268ad9af0a843120d8", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.16.3/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/include/webview_flutter_wkwebview/FWFWebViewConfigurationHostApi.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e988301aca08d99c626c22eeafc1cacd03b", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.16.3/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/include/webview_flutter_wkwebview/FWFWebViewFlutterWKWebViewExternalAPI.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98b6e56e7b51b56098554127bad263bb64", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.16.3/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/include/webview_flutter_wkwebview/FWFWebViewHostApi.h", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98defd531a322e2d1214f7a99899ac2d2f", "name": "webview_flutter_wkwebview", "path": "webview_flutter_wkwebview", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98bf1501e5d880f21a43905b87aee6b850", "name": "include", "path": "include", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e981a60836ea662f1e5c0dce1b401fb05df", "name": "webview_flutter_wkwebview", "path": "webview_flutter_wkwebview", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e983407bd929dc5e69e7cd756669f099538", "name": "Sources", "path": "Sources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98bc31c8155c7710b72626f7fc7efdb58c", "name": "webview_flutter_wkwebview", "path": "webview_flutter_wkwebview", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e984661642821178c65b577fc9b2c340c68", "name": "darwin", "path": "darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e988a5e57aa448094ffab733bba32bfbbd4", "name": "webview_flutter_wkwebview", "path": "webview_flutter_wkwebview", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98046fea9df5ea670c7eab416ac9296235", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9820fa1a2a653c9f0c41dcceb8ee78913f", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9857adb44300cc49c21a11bd329f68da25", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9875931571007c0048fb0a7ff34e24565a", "name": "test_notifictions", "path": "test_notifictions", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98430ef2a7069bc02a8b1259e23459d7ee", "name": "StudioProjects", "path": "StudioProjects", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98564eb30c691539fb21fa6b6cc12286c7", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e981fd3cd7e57f8ac8beb5de5af6e63324f", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9879f20c2a8c97bb97644a6b84dfe82050", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98c611315210bd926c14acaa14386c2d1b", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9862ea7551108e7b6dae70dae4d17ba477", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98e2f2c58be4472f51035852adc5c890c4", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98098c5ddd955b1766c99c4119ba2dcdfe", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9896252b11483d53acf7a6504954f7a230", "name": "..", "path": "../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.16.3/darwin/webview_flutter_wkwebview/Sources", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e98c162f562df7bad1b2666e2f28ce95ad1", "path": "../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.16.3/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/include/FlutterWebView.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e9806a84acf8e8bf86b0e99f2ebef1e4484", "path": "../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.16.3/LICENSE", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e98755c294f90b627899ef5cd130dc59896", "path": "../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.16.3/darwin/webview_flutter_wkwebview.podspec", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e984c44165763aa6edd1a876f3543d4c079", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e9872adba90416292c9d7ad0f15b7cd7b2c", "path": "ResourceBundle-webview_flutter_wkwebview_privacy-webview_flutter_wkwebview-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e983ae8d21dd7b3e58ba27d60d6724d2749", "path": "webview_flutter_wkwebview.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e985b3d6753ecefbfcde609846f829d4f50", "path": "webview_flutter_wkwebview-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98eb8aaaf34308f0db9b40ffd82bf2d4d3", "path": "webview_flutter_wkwebview-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98d602a1505e2ca0874469db0c9b2f71d2", "path": "webview_flutter_wkwebview-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e989f06ba7863a476cc7480b4004f50889a", "path": "webview_flutter_wkwebview.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98b9ef4df0975dd79e07326c11b45588f1", "path": "webview_flutter_wkwebview.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98a32ded93026c5e3111269d9bd54f1c5a", "name": "Support Files", "path": "../../../../Pods/Target Support Files/webview_flutter_wkwebview", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e987cad20e4992523d3afa9cee7e6d2f67e", "name": "webview_flutter_wkwebview", "path": "../.symlinks/plugins/webview_flutter_wkwebview/darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98f03454632b9c465566e0fcc46b23140e", "name": "Development Pods", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"fileType": "wrapper.framework", "guid": "bfdfe7dc352907fc980b868725387e98214b0af25bff664d2c046fcdebe6db71", "path": "Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.0.sdk/System/Library/Frameworks/AVFoundation.framework", "sourceTree": "DEVELOPER_DIR", "type": "file"}, {"fileType": "wrapper.framework", "guid": "bfdfe7dc352907fc980b868725387e98969d7ce10a55440705d72b4af8566ff1", "path": "Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.0.sdk/System/Library/Frameworks/AVKit.framework", "sourceTree": "DEVELOPER_DIR", "type": "file"}, {"fileType": "wrapper.framework", "guid": "bfdfe7dc352907fc980b868725387e9824d585ba56ff05a9a1936345b9ba27ad", "path": "Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.0.sdk/System/Library/Frameworks/CFNetwork.framework", "sourceTree": "DEVELOPER_DIR", "type": "file"}, {"fileType": "wrapper.framework", "guid": "bfdfe7dc352907fc980b868725387e984eaeac3506002d10c4d376c5eec96d29", "path": "Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.0.sdk/System/Library/Frameworks/CoreTelephony.framework", "sourceTree": "DEVELOPER_DIR", "type": "file"}, {"fileType": "wrapper.framework", "guid": "bfdfe7dc352907fc980b868725387e986a42ad16868deef2bc647140f437ecdd", "path": "Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.0.sdk/System/Library/Frameworks/Foundation.framework", "sourceTree": "DEVELOPER_DIR", "type": "file"}, {"fileType": "wrapper.framework", "guid": "bfdfe7dc352907fc980b868725387e9821beea19b226db4fa8bb90fa63b74ab1", "path": "Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.0.sdk/System/Library/Frameworks/ImageIO.framework", "sourceTree": "DEVELOPER_DIR", "type": "file"}, {"fileType": "wrapper.framework", "guid": "bfdfe7dc352907fc980b868725387e98b2a7385a5d9265a265c3799d0bdb1f09", "path": "Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.0.sdk/System/Library/Frameworks/Photos.framework", "sourceTree": "DEVELOPER_DIR", "type": "file"}, {"fileType": "wrapper.framework", "guid": "bfdfe7dc352907fc980b868725387e98a87e7b4aaaddd26ad34f71b042dd0a8c", "path": "Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.0.sdk/System/Library/Frameworks/QuartzCore.framework", "sourceTree": "DEVELOPER_DIR", "type": "file"}, {"fileType": "wrapper.framework", "guid": "bfdfe7dc352907fc980b868725387e98893e4f43ea38aaa1079f62389548f89e", "path": "Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.0.sdk/System/Library/Frameworks/Security.framework", "sourceTree": "DEVELOPER_DIR", "type": "file"}, {"fileType": "wrapper.framework", "guid": "bfdfe7dc352907fc980b868725387e984696e9bfad1bfbbb855166c1ed115504", "path": "Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.0.sdk/System/Library/Frameworks/SystemConfiguration.framework", "sourceTree": "DEVELOPER_DIR", "type": "file"}, {"fileType": "wrapper.framework", "guid": "bfdfe7dc352907fc980b868725387e98a27a22e6958412f527d1f9caa36dc969", "path": "Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.0.sdk/System/Library/Frameworks/UIKit.framework", "sourceTree": "DEVELOPER_DIR", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98edb39ed49d48aea87e43f75b11fc2fc4", "name": "iOS", "path": "", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98c104b1bea68d6abca3456bb310f6dedf", "name": "Frameworks", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98a0e74bb73724268ef67bc7eceab947b6", "path": "Source/Core/AFError.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e989072b56eda70da31f8ffa29c5e2695ab", "path": "Source/Alamofire.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98d231657824b706356b8de98b01133c54", "path": "Source/Features/AlamofireExtended.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9864e360e415519a30a399b79590fb281b", "path": "Source/Features/AuthenticationInterceptor.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98efe32b2ca90f50ee63c77969872c40d2", "path": "Source/Features/CachedResponseHandler.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e980b6aabac013f7e5ce6ebd104e06be45b", "path": "Source/Features/Combine.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9860bee7bf57782d8e5b8cf6c11c3c590a", "path": "Source/Features/Concurrency.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98e718e2ea79e9c7d21c2c98de2bf85856", "path": "Source/Core/DataRequest.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e981e6117609435fb9bbe56fac4e1284614", "path": "Source/Core/DataStreamRequest.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e988c37e9e33e1063e92f13a69636f32fa2", "path": "Source/Extensions/DispatchQueue+Alamofire.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98db926d216f396278551b624a0b8db6fd", "path": "Source/Core/DownloadRequest.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98322ccfddd185fb5cb975597650d3a5d0", "path": "Source/Features/EventMonitor.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9852b06359f1d351956b1d182c908e8a3b", "path": "Source/Core/HTTPHeaders.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e986f43ed6b418156d9158c6e48b7936538", "path": "Source/Core/HTTPMethod.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9800e82a724149d41cbc4a0fd5eafcb2da", "path": "Source/Features/MultipartFormData.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98f19f5e8e43290891cd6c3e3d8a6d98de", "path": "Source/Features/MultipartUpload.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e989dfd89f94623bbef7731ef71daf01385", "path": "Source/Features/NetworkReachabilityManager.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e986bdc1907eb0e575b50def53f17190d4b", "path": "Source/Core/Notifications.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e987293f800b2e055b7dec940377250d6bc", "path": "Source/Extensions/OperationQueue+Alamofire.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9878ecea89b71c4dc5302f1bdea0757b29", "path": "Source/Core/ParameterEncoder.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98cf38baa0955f1348dfb63e595503bc4b", "path": "Source/Core/ParameterEncoding.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98fd3eaf8b1c0766dff853c9a789c1b2f9", "path": "Source/Core/Protected.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98b0b1b1c61cabf84f3a6222c7fd501de3", "path": "Source/Features/RedirectHandler.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98d96485b5483a9ee845f720c2d3d0ff77", "path": "Source/Core/Request.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98812622b2213144f8d6607dd78b5060c6", "path": "Source/Features/RequestCompression.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e985bc90e7767596c67b4571074b7d9efd6", "path": "Source/Features/RequestInterceptor.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9857e9297858372c1596038baa508f3741", "path": "Source/Core/RequestTaskMap.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98c8eaba6025c5478d6c794f64919a9cbd", "path": "Source/Core/Response.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98b575558bccad3e167d18090b5715a3e3", "path": "Source/Features/ResponseSerialization.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98524cd9a20d73f14c032ae1b8244f5aeb", "path": "Source/Extensions/Result+Alamofire.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98432f0253da051af02f344ae20b042cf1", "path": "Source/Features/RetryPolicy.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98e8629eb9eb6908f5770b6f86f63877a4", "path": "Source/Features/ServerTrustEvaluation.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98278727955bdd8c3d545cfc6bc201259e", "path": "Source/Core/Session.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98b497c8e79039c19c08a29597d47e0ffa", "path": "Source/Core/SessionDelegate.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98ff673e70b01c952a811f4392155c9df4", "path": "Source/Extensions/StringEncoding+Alamofire.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98e918c4ef0a18a1083c7562fd72e87e4f", "path": "Source/Core/UploadRequest.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e982a301c430afcd901afb0d50c0a879f3f", "path": "Source/Core/URLConvertible+URLRequestConvertible.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e989c1f8e46cb1c4a48cc140ad02625600f", "path": "Source/Features/URLEncodedFormEncoder.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e981a2424a900a4e4e3b47bf021eaff22b2", "path": "Source/Extensions/URLRequest+Alamofire.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9805d49b518ef07904db48923ff8c2b92c", "path": "Source/Extensions/URLSessionConfiguration+Alamofire.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98a116933f01d2de094fb7295afeace21d", "path": "Source/Features/Validation.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98c626079b6bf2f07e0ee712e162610d2b", "path": "Source/Core/WebSocketRequest.swift", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e98cb91542d14d969bb558a800bb58e2fc5", "path": "Source/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98d4311429884c38b24a63fb55ef68a4db", "name": "Resources", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e982aaea05881e93a0763ad046e02057995", "path": "Alamofire.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98e802133f0e24c20c3f57712f834f6a94", "path": "Alamofire-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98b1c1b5920108a48dd6cc100152252ca2", "path": "Alamofire-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e989f4edad21cbc37c6f7b1315f2e162f52", "path": "Alamofire-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9896a6b0551cdcb8840674407fb1a0f154", "path": "Alamofire-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e9832e40255d8f34c0fcfc95dbda689de2c", "path": "Alamofire.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98f7b6d08f344058faacc72b8ec77fc59f", "path": "Alamofire.release.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e9891a40a6074de1ee8790fe6620352b419", "path": "ResourceBundle-Alamofire-Alamofire-Info.plist", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98a4ced0d37c760b667f7c873d6408ea78", "name": "Support Files", "path": "../Target Support Files/Alamofire", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e983de02373704b90c8a5dcff08e1f072be", "name": "Alamofire", "path": "Alamofire", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98652b9659d84cd071f71804b54a709fde", "path": "Sources/DKImagePickerController/View/Cell/DKAssetGroupCellItemProtocol.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9845eb83627a540de0c90f397e7d6c1e3a", "path": "Sources/DKImagePickerController/View/Cell/DKAssetGroupDetailBaseCell.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98c53abb2450a14aea170e937c56f6f75f", "path": "Sources/DKImagePickerController/View/Cell/DKAssetGroupDetailCameraCell.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98825e3c745840413157dec8731ce83fa2", "path": "Sources/DKImagePickerController/View/Cell/DKAssetGroupDetailImageCell.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98e6baf0fe74b6e6736d1e14cb750b357b", "path": "Sources/DKImagePickerController/View/DKAssetGroupDetailVC.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e986105ba76966694129bf181f5ca2f316a", "path": "Sources/DKImagePickerController/View/Cell/DKAssetGroupDetailVideoCell.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e986639414bc71402a0a3a295c71faf73a5", "path": "Sources/DKImagePickerController/View/DKAssetGroupGridLayout.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98759a4897171864fb4ec8669e3adefb29", "path": "Sources/DKImagePickerController/View/DKAssetGroupListVC.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98841cbd1fb1bc5203ae719dc4d8b83f58", "path": "Sources/DKImagePickerController/DKImageAssetExporter.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98dff583c690192d3f9d2c2c6407f0d512", "path": "Sources/DKImagePickerController/DKImageExtensionController.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98672fb3bbb602291ffdf26fc0dc7f9312", "path": "Sources/DKImagePickerController/DKImagePickerController.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e986c127bd3db15c260cc9b6f1f95768acf", "path": "Sources/DKImagePickerController/DKImagePickerControllerBaseUIDelegate.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e989c3dcd53380711b6d80ccfa2975aa8c8", "path": "Sources/DKImagePickerController/View/DKPermissionView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98da18b5c183e53e9ab9e6cd62f2387e5a", "path": "Sources/DKImagePickerController/DKPopoverViewController.swift", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98bdb04788a177a13ec55620b326fac892", "name": "Core", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9892b732bcb3904580683f12834428e3b7", "path": "Sources/DKImageDataManager/Model/DKAsset.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9853a4015c1316dff4dca1c64c9c7a4f67", "path": "Sources/DKImageDataManager/Model/DKAsset+Export.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9884104da1c7bbb10ddbed9f9a5181b1a8", "path": "Sources/DKImageDataManager/Model/DKAsset+Fetch.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98e1d4f9950e13dd69a0f691dc08c85b88", "path": "Sources/DKImageDataManager/Model/DKAssetGroup.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e980ef45fdf345fdc3d67dd5b65742b3a12", "path": "Sources/DKImageDataManager/DKImageBaseManager.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e980a4903b53a9f9fbab79bc4f0ac1adc30", "path": "Sources/DKImageDataManager/DKImageDataManager.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98a2ba1c0bc36cbe74a325c9d9f383e131", "path": "Sources/DKImageDataManager/DKImageGroupDataManager.swift", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98ff99c4bc71eb2f65db71956228027a9e", "name": "ImageDataManager", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98ede9abf23b719268e948cd719c538781", "path": "Sources/Extensions/DKImageExtensionGallery.swift", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9837a4de11c748b94e585a9f6baa66c88a", "name": "PhotoGallery", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e989b45149c4a7a0beabefe94b62627919b", "path": "Sources/DKImagePickerController/Resource/DKImagePickerControllerResource.swift", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "folder", "guid": "bfdfe7dc352907fc980b868725387e98d4ade5e43d80d88ad25961e370977531", "path": "Sources/DKImagePickerController/Resource/Resources/ar.lproj", "sourceTree": "<group>", "type": "file"}, {"fileType": "folder", "guid": "bfdfe7dc352907fc980b868725387e9873907a4a45898846be5df9198785ef1d", "path": "Sources/DKImagePickerController/Resource/Resources/Base.lproj", "sourceTree": "<group>", "type": "file"}, {"fileType": "folder", "guid": "bfdfe7dc352907fc980b868725387e98e6b399e5230f6506a23f74b371e86411", "path": "Sources/DKImagePickerController/Resource/Resources/da.lproj", "sourceTree": "<group>", "type": "file"}, {"fileType": "folder", "guid": "bfdfe7dc352907fc980b868725387e9837306ec9c2265a14ad5b19996d1827a9", "path": "Sources/DKImagePickerController/Resource/Resources/de.lproj", "sourceTree": "<group>", "type": "file"}, {"fileType": "folder", "guid": "bfdfe7dc352907fc980b868725387e985f15872cc245fe6ef91badb2bf75a3bf", "path": "Sources/DKImagePickerController/Resource/Resources/en.lproj", "sourceTree": "<group>", "type": "file"}, {"fileType": "folder", "guid": "bfdfe7dc352907fc980b868725387e98dfc5fa4442b38cb4aa778a019e1f6f26", "path": "Sources/DKImagePickerController/Resource/Resources/es.lproj", "sourceTree": "<group>", "type": "file"}, {"fileType": "folder", "guid": "bfdfe7dc352907fc980b868725387e9851d3b0ec830beb370e3fdb678b34ebd3", "path": "Sources/DKImagePickerController/Resource/Resources/fr.lproj", "sourceTree": "<group>", "type": "file"}, {"fileType": "folder", "guid": "bfdfe7dc352907fc980b868725387e98f75e3d1fd8ee5242f76865e4e816b6f3", "path": "Sources/DKImagePickerController/Resource/Resources/hu.lproj", "sourceTree": "<group>", "type": "file"}, {"fileType": "folder.assetcatalog", "guid": "bfdfe7dc352907fc980b868725387e98e11dbd4fc70832864d6c7abf0278ac87", "path": "Sources/DKImagePickerController/Resource/Resources/Images.xcassets", "sourceTree": "<group>", "type": "file"}, {"fileType": "folder", "guid": "bfdfe7dc352907fc980b868725387e98746e79c2c0795fadabf414fd5b5ac870", "path": "Sources/DKImagePickerController/Resource/Resources/it.lproj", "sourceTree": "<group>", "type": "file"}, {"fileType": "folder", "guid": "bfdfe7dc352907fc980b868725387e980a7221463fcac28a0c17766acca7a407", "path": "Sources/DKImagePickerController/Resource/Resources/ja.lproj", "sourceTree": "<group>", "type": "file"}, {"fileType": "folder", "guid": "bfdfe7dc352907fc980b868725387e9813c005830baac32b965677a0abb1b292", "path": "Sources/DKImagePickerController/Resource/Resources/ko.lproj", "sourceTree": "<group>", "type": "file"}, {"fileType": "folder", "guid": "bfdfe7dc352907fc980b868725387e985d96c5400647e80f3656b35e9284cbb5", "path": "Sources/DKImagePickerController/Resource/Resources/nb-NO.lproj", "sourceTree": "<group>", "type": "file"}, {"fileType": "folder", "guid": "bfdfe7dc352907fc980b868725387e98d879e505c1d98066b44c0316cb00bc98", "path": "Sources/DKImagePickerController/Resource/Resources/nl.lproj", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e9835e54b88ff0fff67f7081d326a939bad", "path": "Sources/DKImagePickerController/Resource/Resources/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}, {"fileType": "folder", "guid": "bfdfe7dc352907fc980b868725387e9803eac8e20c558f8bc458243e0b562d37", "path": "Sources/DKImagePickerController/Resource/Resources/pt_BR.lproj", "sourceTree": "<group>", "type": "file"}, {"fileType": "folder", "guid": "bfdfe7dc352907fc980b868725387e980f281b3fd0e94a9f3aff4bb965ce8020", "path": "Sources/DKImagePickerController/Resource/Resources/ru.lproj", "sourceTree": "<group>", "type": "file"}, {"fileType": "folder", "guid": "bfdfe7dc352907fc980b868725387e9835d7041541e7dde9af36aece1870b2ed", "path": "Sources/DKImagePickerController/Resource/Resources/tr.lproj", "sourceTree": "<group>", "type": "file"}, {"fileType": "folder", "guid": "bfdfe7dc352907fc980b868725387e98daea1d7b2bbf594743b28abe3aae489d", "path": "Sources/DKImagePickerController/Resource/Resources/ur.lproj", "sourceTree": "<group>", "type": "file"}, {"fileType": "folder", "guid": "bfdfe7dc352907fc980b868725387e9885d078f0739153ac63868488a2a4fa17", "path": "Sources/DKImagePickerController/Resource/Resources/vi.lproj", "sourceTree": "<group>", "type": "file"}, {"fileType": "folder", "guid": "bfdfe7dc352907fc980b868725387e98d92932c6f97f0452c2efe03f54ebcc38", "path": "Sources/DKImagePickerController/Resource/Resources/zh-Hans.lproj", "sourceTree": "<group>", "type": "file"}, {"fileType": "folder", "guid": "bfdfe7dc352907fc980b868725387e98f8ab4f38864957e52868ace55ace6d1f", "path": "Sources/DKImagePickerController/Resource/Resources/zh-Hant.lproj", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98177812463e024d7add44ce526d4ed0dc", "name": "Resources", "path": "", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e985840214ea847be273c72dfd9f14b296c", "name": "Resource", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e98cbd7b567ef82c97927153648d2272896", "path": "DKImagePickerController.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98a27abc9a2213b0bf76ba26118b2d2b88", "path": "DKImagePickerController-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e981d198935b2aee3713b201ae868123e79", "path": "DKImagePickerController-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98f3eed6dc6dab706543951a4934a1b4de", "path": "DKImagePickerController-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e984ca79cf196eb98475789e84f542e4857", "path": "DKImagePickerController-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98e4737d83053e9290cdb2a77d5c915fca", "path": "DKImagePickerController.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e984dae33788bf713be87bfe0a91a4e4274", "path": "DKImagePickerController.release.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e9879c4f06f6bf5a0fc52c603aec30190a5", "path": "ResourceBundle-DKImagePickerController-DKImagePickerController-Info.plist", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9877d53f777176c7d90d2ca00fa5f8550c", "name": "Support Files", "path": "../Target Support Files/DKImagePickerController", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98af48d0afc52884cb63c6f64e129032e2", "name": "DKImagePickerController", "path": "DKImagePickerController", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9853c3069be79213e7ab1234a47088dbb4", "path": "DKPhotoGallery/DKPhotoGallery.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e986a1ecff269206b9b97aef9060baf2b47", "path": "DKPhotoGallery/DKPhotoGalleryContentVC.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98be284f196a9d543d1dc3992bdf666309", "path": "DKPhotoGallery/Transition/DKPhotoGalleryInteractiveTransition.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98277feb8584c7b5f866085f8a2c3084c4", "path": "DKPhotoGallery/DKPhotoGalleryScrollView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e988e0ef67bbd62c3e5887f07a1a49b774d", "path": "DKPhotoGallery/Transition/DKPhotoGalleryTransitionController.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98ff85b1d63548aa26563552ba7f7db23b", "path": "DKPhotoGallery/Transition/DKPhotoGalleryTransitionDismiss.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9859d4388cf4a8e06602c3105a5cb31917", "path": "DKPhotoGallery/Transition/DKPhotoGalleryTransitionPresent.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98027b0aaddf44a50720225282947e1079", "path": "DKPhotoGallery/DKPhotoIncrementalIndicator.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e985a71b4d324d63bcdd7ccfc1b1bec556b", "path": "DKPhotoGallery/DKPhotoPreviewFactory.swift", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98679475c9eec766b6ea3e36cdb8174489", "name": "Core", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98fc70d9a1013e769029a5bf380f89a705", "path": "DKPhotoGallery/DKPhotoGalleryItem.swift", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9818e4bd4f9469fabf8ef3f55682caa911", "name": "Model", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98d2d0685bb3ef2ce6af6c278990a1559f", "path": "DKPhotoGallery/Preview/PDFPreview/DKPDFView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e983079be2e65f03ea0510d47a3008df1fc", "path": "DKPhotoGallery/Preview/ImagePreview/DKPhotoBaseImagePreviewVC.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98f5bc1a07776ac5a0c975d17bd5790a30", "path": "DKPhotoGallery/Preview/DKPhotoBasePreviewVC.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9860ca615c9d06d31ca6f18d667bfa816e", "path": "DKPhotoGallery/Preview/DKPhotoContentAnimationView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98cb28e6ceb0452532874970f96d03401f", "path": "DKPhotoGallery/Preview/ImagePreview/DKPhotoImageDownloader.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98c1094badab1211ab437e7ed26cfc87fe", "path": "DKPhotoGallery/Preview/ImagePreview/DKPhotoImagePreviewVC.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e982b098a4c67c265bdef02a899e7d3b4d0", "path": "DKPhotoGallery/Preview/ImagePreview/DKPhotoImageUtility.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9840c423bb81d3689af9ac6581599d65b7", "path": "DKPhotoGallery/Preview/ImagePreview/DKPhotoImageView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e980af1f034560da099614865475e850838", "path": "DKPhotoGallery/Preview/PDFPreview/DKPhotoPDFPreviewVC.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e986c32773e5e9ff4e671de8784f6a18eaf", "path": "DKPhotoGallery/Preview/PlayerPreview/DKPhotoPlayerPreviewVC.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e987f44d7d4ce6392eb8729535806e2680f", "path": "DKPhotoGallery/Preview/DKPhotoProgressIndicator.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9819018480e9f28f44c146dae53b3e607d", "path": "DKPhotoGallery/Preview/DKPhotoProgressIndicatorProtocol.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e988e8c3316d7d9abf47ee4270f94d7aede", "path": "DKPhotoGallery/Preview/QRCode/DKPhotoQRCodeResultVC.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e985afe7529195eae2e9c976ab74359eb4f", "path": "DKPhotoGallery/Preview/QRCode/DKPhotoWebVC.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9806343f3a8f75530379088c7f42daaee6", "path": "DKPhotoGallery/Preview/PlayerPreview/DKPlayerView.swift", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98aa27896245e683c7979bffdb752cc2b0", "name": "Preview", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9874f7fed31b4908ccbb41233c40d580c3", "path": "DKPhotoGallery/Resource/DKPhotoGalleryResource.swift", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "folder", "guid": "bfdfe7dc352907fc980b868725387e989f30135e889220d19ee03d80434f1aab", "path": "DKPhotoGallery/Resource/Resources/Base.lproj", "sourceTree": "<group>", "type": "file"}, {"fileType": "folder", "guid": "bfdfe7dc352907fc980b868725387e98258346b3089c9a478b89fac01dbee589", "path": "DKPhotoGallery/Resource/Resources/en.lproj", "sourceTree": "<group>", "type": "file"}, {"fileType": "folder.assetcatalog", "guid": "bfdfe7dc352907fc980b868725387e989f3f696971030bc4e3081fd9820b6c6c", "path": "DKPhotoGallery/Resource/Resources/Images.xcassets", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e981eacefccbf9a01f30d9afe523d8c7807", "path": "DKPhotoGallery/Resource/Resources/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}, {"fileType": "folder", "guid": "bfdfe7dc352907fc980b868725387e984ebf001c81bd2a704cc60c7b4aa523ee", "path": "DKPhotoGallery/Resource/Resources/zh-Hans.lproj", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e985123c40ca56f7f2d625c5e1f69199506", "name": "Resources", "path": "", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98fb77ce6f719c451b3ff8fd966c518218", "name": "Resource", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e98adbb39dbc7e3c36130932483c08046c9", "path": "DKPhotoGallery.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e984f9dd5f14089b2df6b08b73a25cc9921", "path": "DKPhotoGallery-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e988ec46ab59ac33b1fec421fa198a0a007", "path": "DKPhotoGallery-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e988f0019a27f0f82df95c39c64ad43b7f6", "path": "DKPhotoGallery-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e987b7d0970ff91cc968227b81d1710693e", "path": "DKPhotoGallery-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98d65889532952df0709294a6081addd54", "path": "DKPhotoGallery.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98b528d7b725a8352ff1e55633ebb2695a", "path": "DKPhotoGallery.release.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e9843af028cdba83ef14c00e92acfce7395", "path": "ResourceBundle-DKPhotoGallery-DKPhotoGallery-Info.plist", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98fe968d767c7243d9ecd83837daeeb62c", "name": "Support Files", "path": "../Target Support Files/DKPhotoGallery", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e980be1c07a34151eeec3bdb7e785e7c3a2", "name": "DKPhotoGallery", "path": "DKPhotoGallery", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98955d01fc4b186505a14d5103efc8ce23", "path": "CoreOnly/Sources/Firebase.h", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98cc9b7c4a2eb7b24b40f0d634cf690f59", "name": "CoreOnly", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98df9c485d6bb78e210a115c003a9e8b3b", "path": "Firebase.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e980220530f5cacb89b1cc36e1ecc31f8dc", "path": "Firebase.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98ac41b924d67d6596a610796de9d0bf94", "name": "Support Files", "path": "../Target Support Files/Firebase", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98c841938b66075fa3cf479e04420d7b41", "name": "Firebase", "path": "Firebase", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98afacbb9b9ca467b2a6d0cadb00aa0a22", "path": "FirebaseCore/Sources/FIRAnalyticsConfiguration.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9832562dbf496589355792a46129460aa5", "path": "FirebaseCore/Sources/FIRAnalyticsConfiguration.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98e505318a7e479d3f0782c91d301dba43", "path": "FirebaseCore/Sources/Public/FirebaseCore/FIRApp.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98b49bfaf712d546a2b8b193f108c3ef9d", "path": "FirebaseCore/Sources/FIRApp.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9883c472307353d04621040c324cea5458", "path": "FirebaseCore/Extension/FIRAppInternal.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98374f36350ffc21a27b8fe149a765f627", "path": "FirebaseCore/Sources/FIRBundleUtil.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9834b0fed48248a93fe916b3ae45fefc0f", "path": "FirebaseCore/Sources/FIRBundleUtil.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e987145c656bdcaea08104239f5d12aa846", "path": "FirebaseCore/Extension/FIRComponent.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98e2380bbfbe351757161a998220a37a8d", "path": "FirebaseCore/Sources/FIRComponent.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e982f90bc3fdd6b99b536ef465395528105", "path": "FirebaseCore/Extension/FIRComponentContainer.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98bb37fd8fdcc06a2dc1a2b80befe6787e", "path": "FirebaseCore/Sources/FIRComponentContainer.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e988a06258749c01e844ce447f0f230314e", "path": "FirebaseCore/Sources/FIRComponentContainerInternal.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e984ea521f400fab7db5769d00c6190014e", "path": "FirebaseCore/Extension/FIRComponentType.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98e36880d1744383df2a11456cfb332026", "path": "FirebaseCore/Sources/FIRComponentType.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98d7c766e5cb9b2c18411bd91af3cef970", "path": "FirebaseCore/Sources/Public/FirebaseCore/FIRConfiguration.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e986a9ada9c56222491b7cd07a0b62d30e4", "path": "FirebaseCore/Sources/FIRConfiguration.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e987e8d8402b7a5dbd11ff3ddb59618c28b", "path": "FirebaseCore/Sources/FIRConfigurationInternal.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e986cda58ffb33e0acb635e268e8af86a4d", "path": "FirebaseCore/Sources/Public/FirebaseCore/FirebaseCore.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9804e3686a1517507b145eabaa43961317", "path": "FirebaseCore/Extension/FirebaseCoreInternal.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98e48a8a5204e99f95512e144a5ee31d23", "path": "FirebaseCore/Sources/FIRFirebaseUserAgent.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e982967862ce619cca97f1d135b877f6528", "path": "FirebaseCore/Sources/FIRFirebaseUserAgent.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98d0e58feb79ce79261ca210c2db9709e5", "path": "FirebaseCore/Extension/FIRHeartbeatLogger.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98e6eb03aaeed80f69682a627786810261", "path": "FirebaseCore/Sources/FIRHeartbeatLogger.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9834da128390d0e3751b1539491caf792d", "path": "FirebaseCore/Extension/FIRLibrary.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98806d6af5333de1e531b5b7969cea441b", "path": "FirebaseCore/Extension/FIRLogger.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9862a7d6ca1a067df162b205a209d3c037", "path": "FirebaseCore/Sources/FIRLogger.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98b6928495ca4ac1d80ab2f76e4ba7a86a", "path": "FirebaseCore/Sources/Public/FirebaseCore/FIRLoggerLevel.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e989c52e53aefb58778d907af4570e3bf2b", "path": "FirebaseCore/Sources/Public/FirebaseCore/FIROptions.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e985cc868834fc68c9942e7f918ee0d147f", "path": "FirebaseCore/Sources/FIROptions.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98d1845d7a80fc3f35fc1f3d8159a56995", "path": "FirebaseCore/Sources/FIROptionsInternal.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9808483f62fa26381691b1678590a3cb78", "path": "FirebaseCore/Sources/Public/FirebaseCore/FIRTimestamp.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98b31bb1f8064a37a19dfbec2d12ddbabe", "path": "FirebaseCore/Sources/FIRTimestamp.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98a2f998e6fd0814d6ac61c60eb383ab1f", "path": "FirebaseCore/Sources/FIRTimestampInternal.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9821a0b91b674b98d21183e8844ae80385", "path": "FirebaseCore/Sources/Public/FirebaseCore/FIRVersion.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98517413d07110cf6ee571eae2304fe660", "path": "FirebaseCore/Sources/FIRVersion.m", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e9898988f2a5aca8ff6d494fa7da37f1f0a", "path": "FirebaseCore/Sources/Resources/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9856f30538f72b1ab3b6a89ba63c110f4c", "name": "Resources", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e9833a8504c8c54d85f5c3f7f17e20d6be2", "path": "FirebaseCore.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98b329835a785226dcc8b1101cb2d59957", "path": "FirebaseCore-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98aab0a5af57e65d05295daa1d9efea2ef", "path": "FirebaseCore-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98b1f6b46684b5c627c8d070a26ef57f7f", "path": "FirebaseCore-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e982aa4132265a6328f13243fd9cef5b52a", "path": "FirebaseCore.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e9837262555d7a8522bf433f7f9553836da", "path": "FirebaseCore.release.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e987b7b81069cb5d4688f61b82f6dbfa4b3", "path": "ResourceBundle-FirebaseCore_Privacy-FirebaseCore-Info.plist", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e989e15486add7f2ffd106bcc26bef39e08", "name": "Support Files", "path": "../Target Support Files/FirebaseCore", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98433f06af476fb7edc10c94da27ad69ae", "name": "FirebaseCore", "path": "FirebaseCore", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9844f879d97754c0e84bc007a0b7658cd4", "path": "FirebaseCore/Internal/Sources/HeartbeatLogging/_ObjC_HeartbeatController.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98cef5ab4948beb5b67b8de8ee0a1f3b81", "path": "FirebaseCore/Internal/Sources/HeartbeatLogging/_ObjC_HeartbeatsPayload.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e981e680b31c01794bc5fd1240c2da5dd49", "path": "FirebaseCore/Internal/Sources/HeartbeatLogging/Heartbeat.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98069fb7d9c007c73bd0bf007a7ec71764", "path": "FirebaseCore/Internal/Sources/HeartbeatLogging/HeartbeatController.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98ac21cb617c99966956cd1f05229baf23", "path": "FirebaseCore/Internal/Sources/HeartbeatLogging/HeartbeatLoggingTestUtils.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98e2da41d6d807ffde73f5a408c0dc35bb", "path": "FirebaseCore/Internal/Sources/HeartbeatLogging/HeartbeatsBundle.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98762fdebb390ab1abcd6ea448af7bc2f6", "path": "FirebaseCore/Internal/Sources/HeartbeatLogging/HeartbeatsPayload.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9805eb981571d93560eccac3783716a025", "path": "FirebaseCore/Internal/Sources/HeartbeatLogging/HeartbeatStorage.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e981fbc83dee6450dccea8d448da0bf4a8d", "path": "FirebaseCore/Internal/Sources/HeartbeatLogging/RingBuffer.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e984d79e22f7982b69a7a603c4c0f2fd332", "path": "FirebaseCore/Internal/Sources/HeartbeatLogging/Storage.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e986b21a91cc736ef8e852f49269217f966", "path": "FirebaseCore/Internal/Sources/HeartbeatLogging/StorageFactory.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98fd7b93252d9da1bdcf5ccd0bea6477ad", "path": "FirebaseCore/Internal/Sources/HeartbeatLogging/WeakContainer.swift", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e9873a7c30b9f0dcec73a58815b404dfb15", "path": "FirebaseCore/Internal/Sources/Resources/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98d61a77c01a49dbc6e6ca8d2650ad59e6", "name": "Resources", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e9825062831333a6dda51da107a3525bb64", "path": "FirebaseCoreInternal.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98b7c9027ee7e0575568c07988d05947ff", "path": "FirebaseCoreInternal-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98962b9d4978b01b4ad9b0731c684faaff", "path": "FirebaseCoreInternal-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98e807290c7b96543ab90093d9098ba84a", "path": "FirebaseCoreInternal-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98ccb9f9da4ae6c7f9a2e7801e8959e4ae", "path": "FirebaseCoreInternal-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98a9c742f4862ace3f00f432433641cb52", "path": "FirebaseCoreInternal.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e9843ce38ad9db5451bfab2a1bfbff1948a", "path": "FirebaseCoreInternal.release.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e985ca802de23b61c51f96b23114a10b4e1", "path": "ResourceBundle-FirebaseCoreInternal_Privacy-FirebaseCoreInternal-Info.plist", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98383805ba76a1128dbf1eb2b095cf4c3a", "name": "Support Files", "path": "../Target Support Files/FirebaseCoreInternal", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98e2c866a5e64d17cc6f54a15b8d98e6ef", "name": "FirebaseCoreInternal", "path": "FirebaseCoreInternal", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e988683b9f8228b13ac1ad9ebd468a0cd51", "path": "FirebaseCore/Extension/FIRAppInternal.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98a2e53f98ec81ced8b706576043e719f5", "path": "FirebaseCore/Extension/FIRComponent.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98e4c9152319fa54c106b273b55f76289e", "path": "FirebaseCore/Extension/FIRComponentContainer.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e980f0afae225386f204cca6bc730211ef8", "path": "FirebaseCore/Extension/FIRComponentType.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98c0d96c9027bd42953deb8470e197c9b6", "path": "FirebaseInstallations/Source/Library/InstallationsIDController/FIRCurrentDateProvider.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9819e777de0f27057e5c87903fe2afef71", "path": "FirebaseInstallations/Source/Library/InstallationsIDController/FIRCurrentDateProvider.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98693e750d2dceb8adb567cbb4da804397", "path": "FirebaseCore/Extension/FirebaseCoreInternal.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9895f814f265277eb5bc14c42480c77bd0", "path": "FirebaseInstallations/Source/Library/Public/FirebaseInstallations/FirebaseInstallations.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98af0c457999b5dfca9a7f7be2b8202315", "path": "FirebaseInstallations/Source/Library/Private/FirebaseInstallationsInternal.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9882250bd94349012238dbf94e6c274d61", "path": "FirebaseCore/Extension/FIRHeartbeatLogger.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e985503d8715cde4892c9e493a9d2b76c9a", "path": "FirebaseInstallations/Source/Library/Public/FirebaseInstallations/FIRInstallations.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98d2bcf5a7939de8c897037c1a22b0173e", "path": "FirebaseInstallations/Source/Library/FIRInstallations.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e985fd9c59fe4d1df4e0430ed4257f4f4b7", "path": "FirebaseInstallations/Source/Library/InstallationsAPI/FIRInstallationsAPIService.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98dcfc3eec27a4ce0bd2155b1d327e80fd", "path": "FirebaseInstallations/Source/Library/InstallationsAPI/FIRInstallationsAPIService.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98bd0eade3b82d6670efc59f72ccac1922", "path": "FirebaseInstallations/Source/Library/Public/FirebaseInstallations/FIRInstallationsAuthTokenResult.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e987de5431142b1d5b8c4cf1b14067bbf0d", "path": "FirebaseInstallations/Source/Library/FIRInstallationsAuthTokenResult.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9813a66b950ecaa3df903d6ebf040c5386", "path": "FirebaseInstallations/Source/Library/FIRInstallationsAuthTokenResultInternal.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98f87b79f827c94f28a31122ca9a61401b", "path": "FirebaseInstallations/Source/Library/InstallationsIDController/FIRInstallationsBackoffController.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e982104851ff637dbab34c9b261b2375bf3", "path": "FirebaseInstallations/Source/Library/InstallationsIDController/FIRInstallationsBackoffController.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e984e48f63d0abcaa4234c5214cf8a7fd38", "path": "FirebaseInstallations/Source/Library/Public/FirebaseInstallations/FIRInstallationsErrors.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98e8b579da8d2a66445c6fbec12db3a675", "path": "FirebaseInstallations/Source/Library/Errors/FIRInstallationsErrorUtil.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9816979394d7666dea83965bce73c283e9", "path": "FirebaseInstallations/Source/Library/Errors/FIRInstallationsErrorUtil.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e983aaf6dd20fa3a4840b07a02d15c963c1", "path": "FirebaseInstallations/Source/Library/Errors/FIRInstallationsHTTPError.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98b9812d7a79e75870f0043bea64ee6ce0", "path": "FirebaseInstallations/Source/Library/Errors/FIRInstallationsHTTPError.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98fe4230ae646ac6036d8b688a035817da", "path": "FirebaseInstallations/Source/Library/InstallationsIDController/FIRInstallationsIDController.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e980b7d17399f275cc275c697b12b639274", "path": "FirebaseInstallations/Source/Library/InstallationsIDController/FIRInstallationsIDController.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e985917750666a4d25f52e1cb35a153b2b8", "path": "FirebaseInstallations/Source/Library/IIDMigration/FIRInstallationsIIDStore.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9885e849bc4cea4c70ef546fe53ff32709", "path": "FirebaseInstallations/Source/Library/IIDMigration/FIRInstallationsIIDStore.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98bdc4dec8fe69c2079ee0cdb4ff1cc7fe", "path": "FirebaseInstallations/Source/Library/IIDMigration/FIRInstallationsIIDTokenStore.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e985f3b756443997e55a9958bcf9ef3a0c9", "path": "FirebaseInstallations/Source/Library/IIDMigration/FIRInstallationsIIDTokenStore.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98cd621f54d93efd821f0151b853774084", "path": "FirebaseInstallations/Source/Library/FIRInstallationsItem.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98b1f7cf445093c6ddd759afeb6e7a8189", "path": "FirebaseInstallations/Source/Library/FIRInstallationsItem.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98cc1935266f3fa0f1d29197cd030fe4e7", "path": "FirebaseInstallations/Source/Library/InstallationsAPI/FIRInstallationsItem+RegisterInstallationAPI.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e986293542a204c5bd9897ec8a2a6aa93f5", "path": "FirebaseInstallations/Source/Library/InstallationsAPI/FIRInstallationsItem+RegisterInstallationAPI.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e986201cba4d245247919b8b972ba1886a4", "path": "FirebaseInstallations/Source/Library/FIRInstallationsLogger.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e987985ef063d4e0813601d6281bb31dfa4", "path": "FirebaseInstallations/Source/Library/FIRInstallationsLogger.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9878de60fa9b4219bf3135a09bace2edec", "path": "FirebaseInstallations/Source/Library/InstallationsIDController/FIRInstallationsSingleOperationPromiseCache.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e981911ca8201147a2101d4acfc1f8be761", "path": "FirebaseInstallations/Source/Library/InstallationsIDController/FIRInstallationsSingleOperationPromiseCache.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9874fe81b91abb7ffdee0e1b81f2509a87", "path": "FirebaseInstallations/Source/Library/InstallationsIDController/FIRInstallationsStatus.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e985da23ce4a4d55ab1291cf4b52467c0bc", "path": "FirebaseInstallations/Source/Library/InstallationsStore/FIRInstallationsStore.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98ba4a06b4c9a04a89e0523a3326205481", "path": "FirebaseInstallations/Source/Library/InstallationsStore/FIRInstallationsStore.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9806405b8ec45ea4c07a6b8bbb859bc010", "path": "FirebaseInstallations/Source/Library/InstallationsStore/FIRInstallationsStoredAuthToken.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98da38aaba6494310eeb9a272667c8f662", "path": "FirebaseInstallations/Source/Library/InstallationsStore/FIRInstallationsStoredAuthToken.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e986ab53d0e4cb57d92c74b127f3bac79b3", "path": "FirebaseInstallations/Source/Library/InstallationsStore/FIRInstallationsStoredItem.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98eed6c939ed6c513127ee66922dcfdfd8", "path": "FirebaseInstallations/Source/Library/InstallationsStore/FIRInstallationsStoredItem.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98a1b0f80a643ee70ee7140e68a8b90e97", "path": "FirebaseCore/Extension/FIRLibrary.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98289fae4757ba3d0cd311712deb895373", "path": "FirebaseCore/Extension/FIRLogger.h", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e98099757d7ea293d84a38db5b375ec0e0c", "path": "FirebaseInstallations/Source/Library/Resources/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98c021a03847f40a4928e0c211bed6ad42", "name": "Resources", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e98d63461a759bfb42ff5eda27ceb447703", "path": "FirebaseInstallations.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98e45009881b93457b7af5d1321ce08576", "path": "FirebaseInstallations-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98ded05d9a5bb2267281dbba4bebe49273", "path": "FirebaseInstallations-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e987f4e5e6f30b42b48b24152350572db94", "path": "FirebaseInstallations-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e981d77ce01b796835e78c0bb0c4a877ce7", "path": "FirebaseInstallations.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e982287609c2a6418a9636a3e58aac224cb", "path": "FirebaseInstallations.release.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e984d1287336469e064ebac5294aaa44c54", "path": "ResourceBundle-FirebaseInstallations_Privacy-FirebaseInstallations-Info.plist", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e984e0d0387214628cbbbea502595a1e1f7", "name": "Support Files", "path": "../Target Support Files/FirebaseInstallations", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98c2fc0f7dc9c26ff8c2db2fede0365728", "name": "FirebaseInstallations", "path": "FirebaseInstallations", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e985e03d8197ad3826e208551668915db5d", "path": "Interop/Analytics/Public/FIRAnalyticsInterop.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98a9b6ac9152ab9e320b94f103e418902b", "path": "Interop/Analytics/Public/FIRAnalyticsInteropListener.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9855717f5c70c24f94f752e7bfb97d0881", "path": "FirebaseCore/Extension/FIRAppInternal.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98b7305c3ee4a3467e971aa0e2cd26427f", "path": "FirebaseCore/Extension/FIRComponent.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9871606f6777059051a3803358a92425ec", "path": "FirebaseCore/Extension/FIRComponentContainer.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9858abb815deeec9f91909997213d35d4d", "path": "FirebaseCore/Extension/FIRComponentType.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98704007555d10d5a68a5a68206e5365a0", "path": "FirebaseCore/Extension/FirebaseCoreInternal.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9876be1104ccb0baab2a43b04a64b8f6c6", "path": "FirebaseInstallations/Source/Library/Private/FirebaseInstallationsInternal.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98f61003b97defee90f3e3a412c5a315ee", "path": "FirebaseMessaging/Sources/FirebaseMessaging.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98e6b4bb5fc0babee707559619fc36a5f7", "path": "FirebaseMessaging/Sources/Public/FirebaseMessaging/FirebaseMessaging.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98d261215f365abde8d0c5250d79337514", "path": "FirebaseCore/Extension/FIRHeartbeatLogger.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98207d95c6d12e0f7f716de150f40723fa", "path": "Interop/Analytics/Public/FIRInteropEventNames.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e984e07ed5f3fa6e4d3d06dd581e2c758d3", "path": "Interop/Analytics/Public/FIRInteropParameterNames.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98f116cb96213664c772f6408c9cbbc5ca", "path": "FirebaseCore/Extension/FIRLibrary.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98fe25117b5464925c41ef36f50a9d8b23", "path": "FirebaseCore/Extension/FIRLogger.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9813e4805cd8fccbdbfe7930d407233f97", "path": "FirebaseMessaging/Sources/Public/FirebaseMessaging/FIRMessaging.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98351f16b56b6e7689b4fdee0165643ff8", "path": "FirebaseMessaging/Sources/FIRMessaging.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9895d6964853f476c306349efdafd0fab8", "path": "FirebaseMessaging/Sources/Public/FirebaseMessaging/FIRMessaging+ExtensionHelper.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98fe16759492ede82e502b491ae4f39a86", "path": "FirebaseMessaging/Sources/FIRMessaging+ExtensionHelper.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98cba20ce2640d568df8e22340462968b8", "path": "FirebaseMessaging/Sources/FIRMessaging_Private.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98261dc02439779d4767ce14e15cd8da82", "path": "FirebaseMessaging/Sources/FIRMessagingAnalytics.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e988c8a9da27262d32cb31318d0ab52c588", "path": "FirebaseMessaging/Sources/FIRMessagingAnalytics.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98b2d18ef45cfe069881c2ad8411ed95ef", "path": "FirebaseMessaging/Sources/Token/FIRMessagingAPNSInfo.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e981df4f67cc8e7791c14bd71e2bb6753fc", "path": "FirebaseMessaging/Sources/Token/FIRMessagingAPNSInfo.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e981690e58147f407e1971367fcd27e1e5e", "path": "FirebaseMessaging/Sources/Token/FIRMessagingAuthKeychain.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98a2e72af4f6d8a803b9917214f05b7cc2", "path": "FirebaseMessaging/Sources/Token/FIRMessagingAuthKeychain.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9829adf467d60935dc1667c66b9fd50fb8", "path": "FirebaseMessaging/Sources/Token/FIRMessagingAuthService.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98549f9f6b7ea9236a6eef9051faa9d5b0", "path": "FirebaseMessaging/Sources/Token/FIRMessagingAuthService.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98eab63fe29fb7a3adce718131a2be6291", "path": "FirebaseMessaging/Sources/Token/FIRMessagingBackupExcludedPlist.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e984b522f050049e263bce572bc5420d46f", "path": "FirebaseMessaging/Sources/Token/FIRMessagingBackupExcludedPlist.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9818287c3724ab637dd18908cb0db12228", "path": "FirebaseMessaging/Sources/Token/FIRMessagingCheckinPreferences.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98a33a9e1e551f2c745e8c732431435e38", "path": "FirebaseMessaging/Sources/Token/FIRMessagingCheckinPreferences.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e982ba6672f94baa6e8e4dd2116e78ea856", "path": "FirebaseMessaging/Sources/Token/FIRMessagingCheckinService.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e985c1fadc162642a42096edb373ba17371", "path": "FirebaseMessaging/Sources/Token/FIRMessagingCheckinService.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98a1c3369c47142f8700744b1d2ec13f5a", "path": "FirebaseMessaging/Sources/Token/FIRMessagingCheckinStore.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98d4a9b34a2bdc5e8f362e5346ca43bef3", "path": "FirebaseMessaging/Sources/Token/FIRMessagingCheckinStore.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9814647a30f2940a8bf2ddfd0e9e47bd68", "path": "FirebaseMessaging/Sources/FIRMessagingCode.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98fa5cd3a595077da30cf42baa3720a473", "path": "FirebaseMessaging/Sources/FIRMessagingConstants.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9800def94e971d43e57ac07195535985d2", "path": "FirebaseMessaging/Sources/FIRMessagingConstants.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98e7af93077154dc3656bffabf4afe200a", "path": "FirebaseMessaging/Sources/FIRMessagingContextManagerService.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98dc5ef336dc67f076db0c1897f1cf95b3", "path": "FirebaseMessaging/Sources/FIRMessagingContextManagerService.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98bca00a84939a42004d9d127e6588bd78", "path": "FirebaseMessaging/Sources/FIRMessagingDefines.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98f41e468b1ae195cb9100df8fea649ba4", "path": "FirebaseMessaging/Sources/Public/FirebaseMessaging/FIRMessagingExtensionHelper.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98a4aeffdceaf712d95eb385974278b178", "path": "FirebaseMessaging/Sources/FIRMessagingExtensionHelper.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98904f28085bdfc34ffff9818f964d6c0c", "path": "FirebaseMessaging/Interop/FIRMessagingInterop.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e986cf26a8dc3c75ca19e204bee4034cbac", "path": "FirebaseMessaging/Sources/Token/FIRMessagingKeychain.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9817736c4de1af1767df85d1dee90e9a38", "path": "FirebaseMessaging/Sources/Token/FIRMessagingKeychain.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e983a3bb1bb9174c06e120dc014575ce7e2", "path": "FirebaseMessaging/Sources/FIRMessagingLogger.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e981f7972ccf059c155e979cdda06c95d3c", "path": "FirebaseMessaging/Sources/FIRMessagingLogger.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98cf5ba6e476dcf31fdf5ef9e20d12e857", "path": "FirebaseMessaging/Sources/FIRMessagingPendingTopicsList.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e985ee678721226bd20bbfe31eb175b494d", "path": "FirebaseMessaging/Sources/FIRMessagingPendingTopicsList.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98f4b5ec467528d93d3e349fcf203ecb2e", "path": "FirebaseMessaging/Sources/FIRMessagingPersistentSyncMessage.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e982b10ae618d85defe860da7aad021c471", "path": "FirebaseMessaging/Sources/FIRMessagingPersistentSyncMessage.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e981b3665452c65a8dd460571ef98cc16d0", "path": "FirebaseMessaging/Sources/FIRMessagingPubSub.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98fde0fbe54ea7f39c8c25a46cc6db1570", "path": "FirebaseMessaging/Sources/FIRMessagingPubSub.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9833f1060473e4618fe6e6aedb26f1d027", "path": "FirebaseMessaging/Sources/FIRMessagingRemoteNotificationsProxy.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9862b2b1e2ca436572a59364af5bf3966c", "path": "FirebaseMessaging/Sources/FIRMessagingRemoteNotificationsProxy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9804cb220d1a338217dd4826753cbfa367", "path": "FirebaseMessaging/Sources/FIRMessagingRmqManager.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98b657464eca7ba0c83e3a519c53927606", "path": "FirebaseMessaging/Sources/FIRMessagingRmqManager.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e981963fc9e561bd36d1d4a104ec82b3637", "path": "FirebaseMessaging/Sources/FIRMessagingSyncMessageManager.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98c167739fe2f661019ffe7e9467f543c4", "path": "FirebaseMessaging/Sources/FIRMessagingSyncMessageManager.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e989726007439fc8605fd6b1caf0fc96b9d", "path": "FirebaseMessaging/Sources/Token/FIRMessagingTokenDeleteOperation.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98c908393a251827986537fa7e787e2864", "path": "FirebaseMessaging/Sources/Token/FIRMessagingTokenDeleteOperation.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e981a59ac2a258f4432092b827d46b5d261", "path": "FirebaseMessaging/Sources/Token/FIRMessagingTokenFetchOperation.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98ba326415df0efa3c04558eac1324f6b8", "path": "FirebaseMessaging/Sources/Token/FIRMessagingTokenFetchOperation.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e988860a7197d4c9873228aab39e9370c9a", "path": "FirebaseMessaging/Sources/Token/FIRMessagingTokenInfo.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98a3853e605d7ec722097db7bc8cb1aee3", "path": "FirebaseMessaging/Sources/Token/FIRMessagingTokenInfo.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9813e629cea8d0b838c2aa030bcdcffc79", "path": "FirebaseMessaging/Sources/Token/FIRMessagingTokenManager.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98cf758e7cef288154e13d6455388467d5", "path": "FirebaseMessaging/Sources/Token/FIRMessagingTokenManager.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9817d4788e74e82c2a7c1bcca0be9020c5", "path": "FirebaseMessaging/Sources/Token/FIRMessagingTokenOperation.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98ef95835f01aa1500df85ca46ac6376f2", "path": "FirebaseMessaging/Sources/Token/FIRMessagingTokenOperation.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e982febca23ea8cd9ae74e3b2f5a1c9da5e", "path": "FirebaseMessaging/Sources/Token/FIRMessagingTokenStore.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98ec42050cdda697dba555a4c7ca5700a7", "path": "FirebaseMessaging/Sources/Token/FIRMessagingTokenStore.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98727aa8c74ce88de9269288deec350f39", "path": "FirebaseMessaging/Sources/FIRMessagingTopicOperation.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98a72d0a2d28221646aaf29c6dfb6748c1", "path": "FirebaseMessaging/Sources/FIRMessagingTopicOperation.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98de24186d7eaa0695c7063541798e477b", "path": "FirebaseMessaging/Sources/FIRMessagingTopicsCommon.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9844b60bd8f742c564f9629ebd85bccd51", "path": "FirebaseMessaging/Sources/FIRMessagingUtilities.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e986e55d341b224b721682b07e3b19ecb54", "path": "FirebaseMessaging/Sources/FIRMessagingUtilities.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e9887f4fa893a7300288731ab874ed2cf56", "path": "FirebaseMessaging/Sources/Protogen/nanopb/me.nanopb.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98c46b4efc484a1b6f5b4af3df9870d060", "path": "FirebaseMessaging/Sources/Protogen/nanopb/me.nanopb.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e981d6f1c42e95884c61ff140177d41074b", "path": "FirebaseMessaging/Sources/NSDictionary+FIRMessaging.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98c59e8c29df0bd0bd996875aa66ed6c3b", "path": "FirebaseMessaging/Sources/NSDictionary+FIRMessaging.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e984678eac2e3587712532cf36a386b0d35", "path": "FirebaseMessaging/Sources/NSError+FIRMessaging.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98beeb831bed537b3c24e2216eacc2dc84", "path": "FirebaseMessaging/Sources/NSError+FIRMessaging.m", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e98f40808adcfddfead4c13e0ab8ff953b5", "path": "FirebaseMessaging/Sources/Resources/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9810285455f251f1b8d103b4a1e5dc9aa0", "name": "Resources", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e983c529b54e98c552c3ba7513fd6442b73", "path": "FirebaseMessaging.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98bdef49fb90fb5816b7f316eb11908dca", "path": "FirebaseMessaging-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98912f114c61b58ea0d3024dd989eb4ea4", "path": "FirebaseMessaging-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9810b66d77908456a61dd926ca7d1f9376", "path": "FirebaseMessaging-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e9838e29c285c0cde6989afb05fa38e9dad", "path": "FirebaseMessaging.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98950d8ab69ba11cc7d5947d3a2dff7832", "path": "FirebaseMessaging.release.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98a8fafd48077ea74c52546fa8b8e7a4e0", "path": "ResourceBundle-FirebaseMessaging_Privacy-FirebaseMessaging-Info.plist", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9829480b6bfab3dff634b6be42b39a5085", "name": "Support Files", "path": "../Target Support Files/FirebaseMessaging", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98ca3e8bd8e44588718437d893d5337637", "name": "FirebaseMessaging", "path": "FirebaseMessaging", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9894401c5477dfb68d012994ef6cba0311", "path": "Sources/GoogleMapsUtils/GeometryUtils/Internal/CartesianPoint.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98e84a4006b148cd5bd78c95b70a9b2405", "path": "Sources/GoogleMapsUtils/GeometryUtils/CLLocationCoordinate2D+GeometryUtils.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98f1455d5c7ef94e500155970570739cff", "path": "Sources/GoogleMapsUtilsObjC/include/GMSMarker+GMUClusteritem.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98141304433e6ae918966ad5d6674ec179", "path": "Sources/GoogleMapsUtilsObjC/include/GMSMarker+GMUClusteritem.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98b4ab38d78b53a72a553777e44eb78661", "path": "Sources/GoogleMapsUtils/GeometryUtils/GMSPath+GeometryUtils.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98d2369d5d3fe4ee4ecdaf14c2c05f7593", "path": "Sources/GoogleMapsUtils/GeometryUtils/GMSPolygon+GeometryUtils.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98f3156948c010edff0f46863f34d1c640", "path": "Sources/GoogleMapsUtils/GeometryUtils/GMSPolyline+GeometryUtils.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98fd335906d87d01453fd014a3fd91c6be", "path": "Sources/GoogleMapsUtilsObjC/include/GMUCluster.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9881f5899c8c40e7ad85882b147748c9e8", "path": "Sources/GoogleMapsUtilsObjC/include/GMUClusterAlgorithm.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98a50d1dac04d4edd603fd9d871352e8d0", "path": "Sources/GoogleMapsUtilsObjC/include/GMUClusterIconGenerator.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e983f20e808fd0df397578aeb87e3402ef4", "path": "Sources/GoogleMapsUtilsObjC/include/GMUClusterItem.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e987e2ef4d6f33a5351113902e64d0a7cc8", "path": "Sources/GoogleMapsUtilsObjC/include/GMUClusterManager.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98b709ac4de6ee37116c75f8b317dc2e89", "path": "Sources/GoogleMapsUtilsObjC/include/GMUClusterManager.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98ddaa3ddabbd926ee263d77eb9e2b700b", "path": "Sources/GoogleMapsUtilsObjC/include/GMUClusterManager+Testing.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e980c294d4f0e32c0908e6a35eb6694b8e9", "path": "Sources/GoogleMapsUtilsObjC/include/GMUClusterRenderer.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98bddaaa1fac438424ab3d6f8753dbba2a", "path": "Sources/GoogleMapsUtilsObjC/include/GMUDefaultClusterIconGenerator.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9808f3c1335475c33501be9249e548b199", "path": "Sources/GoogleMapsUtilsObjC/include/GMUDefaultClusterIconGenerator.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98ec0c33b35276869296038486ab56dcec", "path": "Sources/GoogleMapsUtilsObjC/include/GMUDefaultClusterIconGenerator+Testing.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9807773d9a874b026d7ad16396f422f2dd", "path": "Sources/GoogleMapsUtilsObjC/include/GMUDefaultClusterRenderer.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e986f6a8b4df9e434bf6f029c30882d6fc6", "path": "Sources/GoogleMapsUtilsObjC/include/GMUDefaultClusterRenderer.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9851c98d163d1fe02458b3b4d940c70e1c", "path": "Sources/GoogleMapsUtilsObjC/include/GMUDefaultClusterRenderer+Testing.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98a3beecfeeba980f518b00ac664d7a5ff", "path": "Sources/GoogleMapsUtilsObjC/include/GMUFeature.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9810c431c130ca0b6ad68644ba62992cf1", "path": "Sources/GoogleMapsUtilsObjC/include/GMUFeature.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e984a6c0c66f3b72737a271434b906c4bec", "path": "Sources/GoogleMapsUtilsObjC/include/GMUGeoJSONParser.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98dd5502205be9b14a218424e89697285f", "path": "Sources/GoogleMapsUtilsObjC/include/GMUGeoJSONParser.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9885e8dfd3f98cb98b224b0e6b081c0007", "path": "Sources/GoogleMapsUtilsObjC/include/GMUGeometry.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9864c7fda0f21fbbd9f4f8fcc7192ecc8f", "path": "Sources/GoogleMapsUtilsObjC/include/GMUGeometryCollection.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e983cb16639513adeda9fec86e72d8c3e2b", "path": "Sources/GoogleMapsUtilsObjC/include/GMUGeometryCollection.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e983211cc13d3466669078115ea24106260", "path": "Sources/GoogleMapsUtilsObjC/include/GMUGeometryContainer.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e982ee5409a6669660d6aea46c9cb7cd139", "path": "Sources/GoogleMapsUtilsObjC/include/GMUGeometryRenderer.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9883753b94ff707686d3e5c50ef53c6945", "path": "Sources/GoogleMapsUtilsObjC/include/GMUGeometryRenderer.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98e57b35dd70505e5e1444ad0383e0589a", "path": "Sources/GoogleMapsUtilsObjC/include/GMUGeometryRenderer+Testing.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98006236376e4e14a5d540ef399c6bc4ca", "path": "Sources/GoogleMapsUtilsObjC/include/GMUGradient.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98b0f6b5918b8838fb9e828851fa7b35f3", "path": "Sources/GoogleMapsUtilsObjC/include/GMUGradient.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e981d64d17761885d63ee66d65b204e96e6", "path": "Sources/GoogleMapsUtilsObjC/include/GMUGridBasedClusterAlgorithm.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9822388bfbc7cd52792a080856cf13ca16", "path": "Sources/GoogleMapsUtilsObjC/include/GMUGridBasedClusterAlgorithm.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9847fb2fc5f32336e802ef8523af225dfd", "path": "Sources/GoogleMapsUtilsObjC/include/GMUGroundOverlay.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98d607a37dc2e13277ee92fc8d3b24719d", "path": "Sources/GoogleMapsUtilsObjC/include/GMUGroundOverlay.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e982c9fc1041f7fc4c9ba6c0c1637154e58", "path": "Sources/GoogleMapsUtilsObjC/include/GMUHeatmapTileLayer.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e987795f9031a7e97eec1d84c610ad04a80", "path": "Sources/GoogleMapsUtilsObjC/include/GMUHeatmapTileLayer.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e985b6c4f095a3b49e452d5a8a44106743d", "path": "Sources/GoogleMapsUtilsObjC/include/GMUHeatmapTileLayer+Testing.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98581344d0b71b76b236f479728371a99d", "path": "Sources/GoogleMapsUtilsObjC/include/GMUKMLParser.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98d09102fdb86f820e87ad6f9ebf4d426d", "path": "Sources/GoogleMapsUtilsObjC/include/GMUKMLParser.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e989fe5d2560a1229d4c85c20e582f884c8", "path": "Sources/GoogleMapsUtilsObjC/include/GMULineString.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98989481a8e26a9bd3a1aa417f3a6f0cd9", "path": "Sources/GoogleMapsUtilsObjC/include/GMULineString.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e986a1672fc2264bccd2e1cbd7fae01d24d", "path": "Sources/GoogleMapsUtilsObjC/include/GMUMarkerClustering.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9856f55316bb4e5f26ea974fccbdb96691", "path": "Sources/GoogleMapsUtilsObjC/include/GMUNonHierarchicalDistanceBasedAlgorithm.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98a7324c3cafef334131f7a0711bc882db", "path": "Sources/GoogleMapsUtilsObjC/include/GMUNonHierarchicalDistanceBasedAlgorithm.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e981ebc65ce3f29d4f5b6a353fc5a1a75b5", "path": "Sources/GoogleMapsUtilsObjC/include/GMUPair.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98a1269c13097020b65a4e8783979deefe", "path": "Sources/GoogleMapsUtilsObjC/include/GMUPair.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e985c0f9d914f24681da02f638c7eba255b", "path": "Sources/GoogleMapsUtilsObjC/include/GMUPlacemark.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98bd63baa760a0134269fe0e7a7cfdec5b", "path": "Sources/GoogleMapsUtilsObjC/include/GMUPlacemark.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e986f2d6e21fd3ae7eababee7c7b1648b7e", "path": "Sources/GoogleMapsUtilsObjC/include/GMUPoint.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e987549cbb48023efb2c8a451347ce8f502", "path": "Sources/GoogleMapsUtilsObjC/include/GMUPoint.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9820f6ea39e38f8b6fa8c1fd78cf5eae2b", "path": "Sources/GoogleMapsUtilsObjC/include/GMUPolygon.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98a9ccfd8c7492310be09644367e180f8e", "path": "Sources/GoogleMapsUtilsObjC/include/GMUPolygon.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98664d0c8b65acf310088e9766c3a952d3", "path": "Sources/GoogleMapsUtilsObjC/include/GMUSimpleClusterAlgorithm.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98ba307222a82e187e3bcc5f4e2e8b9f25", "path": "Sources/GoogleMapsUtilsObjC/include/GMUSimpleClusterAlgorithm.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9845ad23ec973974aa15583df53c6db7c0", "path": "Sources/GoogleMapsUtilsObjC/include/GMUStaticCluster.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e983ca214fbe8a579fbb4560462cd731aa0", "path": "Sources/GoogleMapsUtilsObjC/include/GMUStaticCluster.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98e104ead610d97aa84d3e1ca9fecdf01a", "path": "Sources/GoogleMapsUtilsObjC/include/GMUStyle.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98f9f4b593a60fd1c3caed8e98920c7026", "path": "Sources/GoogleMapsUtilsObjC/include/GMUStyle.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98324644c0245083f40b5f4f685bd6c09f", "path": "Sources/GoogleMapsUtilsObjC/include/GMUStyleMap.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9837e517b70af22a4ac5ff0f14af315c08", "path": "Sources/GoogleMapsUtilsObjC/include/GMUStyleMap.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9822d5ac2fd89bc0ab7a08b072907baa99", "path": "Sources/GoogleMapsUtilsObjC/include/GMUWeightedLatLng.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9868a2e1add618488d9764ff22103d5948", "path": "Sources/GoogleMapsUtilsObjC/include/GMUWeightedLatLng.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98d578c10ff59accf12db51f6906358cc1", "path": "Sources/GoogleMapsUtilsObjC/include/GMUWrappingDictionaryKey.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9821c92097a43bcbecb165724a956cacfe", "path": "Sources/GoogleMapsUtilsObjC/include/GMUWrappingDictionaryKey.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9845c900cdf9b9b1cf517cc602745d0c99", "path": "Sources/GoogleMapsUtilsObjC/include/GoogleMapsUtils-Bridging-Header.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98116e9b59774b0af84cb09bccc8a748d5", "path": "Sources/GoogleMapsUtilsObjC/include/GQTBounds.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e986a1740f398b354fa8ebc654f5212e4a1", "path": "Sources/GoogleMapsUtilsObjC/include/GQTPoint.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98f596e252115fb744c42a8fea7e7f8213", "path": "Sources/GoogleMapsUtilsObjC/include/GQTPointQuadTree.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e989d55bd38ded96c19503fbcbac6b16e80", "path": "Sources/GoogleMapsUtilsObjC/include/GQTPointQuadTree.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e983054fc3aafe7808940aaecb6003c3c15", "path": "Sources/GoogleMapsUtilsObjC/include/GQTPointQuadTreeChild.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98511617102fac8028651a8bc740c7e4f4", "path": "Sources/GoogleMapsUtilsObjC/include/GQTPointQuadTreeChild.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98224ef9a1e5c5e3b517c3983873d9f784", "path": "Sources/GoogleMapsUtilsObjC/include/GQTPointQuadTreeItem.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98c0e0235681e60d4db1a6c4d868ebc175", "path": "Sources/GoogleMapsUtils/Heatmap/HeatmapInterpolationPoints.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9880c16ecbb751c83e04dced73d494804c", "path": "Sources/GoogleMapsUtils/GeometryUtils/Internal/LatLngRadians.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98025a516da3858af641f6e7a2bb0af9f1", "path": "Sources/GoogleMapsUtils/GeometryUtils/MapPoint.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e983096b33d4dd3bf340b4d45b838ef79c0", "path": "Sources/GoogleMapsUtils/GeometryUtils/Math.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e983efdb556b35ad5e63bbbe1b5a07bec7d", "path": "Sources/GoogleMapsUtils/Helper/MockMapView.swift", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e980175c8aa252c19c96adc0562a762ce92", "path": "Google-Maps-iOS-Utils.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e986e3240ae2a610b725291d5d10bfae628", "path": "Google-Maps-iOS-Utils-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e985f1fc26c005e5251d4a53842c327b1fa", "path": "Google-Maps-iOS-Utils-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9826f6ee9bc335e5519e8c8f7fe50acabb", "path": "Google-Maps-iOS-Utils-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98046dc05462afbd79f5d8b5c22962a5f6", "path": "Google-Maps-iOS-Utils-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e9837bfc7a8a696282219023b855323c032", "path": "Google-Maps-iOS-Utils.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98c5c5152f325dadceb18584542905cb74", "path": "Google-Maps-iOS-Utils.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98c210fb939c6d3de66e644dfd49f4db6f", "name": "Support Files", "path": "../Target Support Files/Google-Maps-iOS-Utils", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e981c697d8926b2bec9c99881627c379cab", "name": "Google-Maps-iOS-Utils", "path": "Google-Maps-iOS-Utils", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e989990b92aed02b02d21a3b676fc19bfa9", "path": "Sources/GoogleMobileAdsPlaceholder.swift", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "wrapper.xcframework", "guid": "bfdfe7dc352907fc980b868725387e98756b884a98c91e4e4b09437af3b2944f", "path": "Frameworks/GoogleMobileAdsFramework/GoogleMobileAds.xcframework", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98685f280ed6b717f981ae74c1c36f0414", "name": "Frameworks", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e98a5316c3d650d1b7137e944d80ae282f9", "path": "Frameworks/GoogleMobileAdsFramework/GoogleMobileAds.xcframework/ios-arm64/GoogleMobileAds.framework/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9810a8271f6b3b6f5678633133cf5452ee", "name": "Resources", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e9825ce39f36a3b6fa0bd8addf433424e4c", "path": "Google-Mobile-Ads-SDK.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98f35023df895ad857df8f3aafe3c63bea", "path": "Google-Mobile-Ads-SDK-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98b8ca7c8329e9320ad3c68fe33d67542c", "path": "Google-Mobile-Ads-SDK-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9819776bd7e36dd611f6e57726c00087c1", "path": "Google-Mobile-Ads-SDK-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9896435920244dfcd00de3a743d3d7bbd9", "path": "Google-Mobile-Ads-SDK-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.script.sh", "guid": "bfdfe7dc352907fc980b868725387e980d322b1d11fda841d38eb0efdd504dc9", "path": "Google-Mobile-Ads-SDK-xcframeworks.sh", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98953acbb4359b13573456a60e136e883d", "path": "Google-Mobile-Ads-SDK.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e984a715eefe9233a48988041d6412011dd", "path": "Google-Mobile-Ads-SDK.release.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e984672d732788fb4062517676ac33768da", "path": "ResourceBundle-GoogleMobileAdsResources-Google-Mobile-Ads-SDK-Info.plist", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98f3190bb85282df29e90a2327bf3d1b6e", "name": "Support Files", "path": "../Target Support Files/Google-Mobile-Ads-SDK", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9861f2f60e594326541f2c7ae20c9b0e7a", "name": "Google-Mobile-Ads-SDK", "path": "Google-Mobile-Ads-SDK", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e986817171df4293279f9ee7355a594e03f", "path": "GoogleDataTransport/GDTCCTLibrary/Protogen/nanopb/cct.nanopb.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98218e305c8aecd93be5bdfb06a18acdaf", "path": "GoogleDataTransport/GDTCCTLibrary/Protogen/nanopb/cct.nanopb.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e988a3435aac00f4f3c2025610e9b2dd06c", "path": "GoogleDataTransport/GDTCCTLibrary/Protogen/nanopb/client_metrics.nanopb.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9868d439c7e67f926540c1793d1c02a715", "path": "GoogleDataTransport/GDTCCTLibrary/Protogen/nanopb/client_metrics.nanopb.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e9877671ad2e5d99d89d09c7484e9a477b5", "path": "GoogleDataTransport/GDTCCTLibrary/Protogen/nanopb/compliance.nanopb.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98385e1337ec8794cd4257ef8cb7c5cc93", "path": "GoogleDataTransport/GDTCCTLibrary/Protogen/nanopb/compliance.nanopb.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e988c8a7af92782786d6cf03a12fc7d0340", "path": "GoogleDataTransport/GDTCCTLibrary/Protogen/nanopb/external_prequest_context.nanopb.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9838104432646bdaa4275d069ab23759d4", "path": "GoogleDataTransport/GDTCCTLibrary/Protogen/nanopb/external_prequest_context.nanopb.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e98fe11db3f2e674478b1c3a1d5d6ed41c0", "path": "GoogleDataTransport/GDTCCTLibrary/Protogen/nanopb/external_privacy_context.nanopb.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98854b5d4d4c7ea31f982c4ef727edef31", "path": "GoogleDataTransport/GDTCCTLibrary/Protogen/nanopb/external_privacy_context.nanopb.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98352e4127129623b58d91f3ac9f33b7ef", "path": "GoogleDataTransport/GDTCCTLibrary/Private/GDTCCTCompressionHelper.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98f6a7f666d85955ec4b3b71ed0953dede", "path": "GoogleDataTransport/GDTCCTLibrary/GDTCCTCompressionHelper.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9867c5ef02a4c5b3868cfd39f6668bbb9d", "path": "GoogleDataTransport/GDTCCTLibrary/Private/GDTCCTNanopbHelpers.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e989cd1895cb8d1dbbf286329af18f138a7", "path": "GoogleDataTransport/GDTCCTLibrary/GDTCCTNanopbHelpers.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e988783557037552ba305a7d42c14b8023f", "path": "GoogleDataTransport/GDTCCTLibrary/Private/GDTCCTUploader.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98f54683bacde61afa143ccc796e283548", "path": "GoogleDataTransport/GDTCCTLibrary/GDTCCTUploader.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e989da1f69798a6a07befc6eeea76658f53", "path": "GoogleDataTransport/GDTCCTLibrary/Private/GDTCCTUploadOperation.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9840f960fbf91af767bff912c97391beb5", "path": "GoogleDataTransport/GDTCCTLibrary/GDTCCTUploadOperation.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e987a964b1edc586fa981db0b6bbc9f6f62", "path": "GoogleDataTransport/GDTCCTLibrary/Private/GDTCCTURLSessionDataResponse.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e980f5eca5408bce7138d7c20adfb6ec771", "path": "GoogleDataTransport/GDTCCTLibrary/GDTCCTURLSessionDataResponse.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e984511be9b4ad04d4968337da80ef1591e", "path": "GoogleDataTransport/GDTCORLibrary/Internal/GDTCORAssert.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98d98f4b06d09a2303e07f3016eb488159", "path": "GoogleDataTransport/GDTCORLibrary/GDTCORAssert.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9805564f30adff4b22686fd02c10cc731e", "path": "GoogleDataTransport/GDTCORLibrary/Public/GoogleDataTransport/GDTCORClock.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e989b812f7de9c962daa4b0f47e4a427c5c", "path": "GoogleDataTransport/GDTCORLibrary/GDTCORClock.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98f5b8de3e41649cd6b59b30aacf8c7a53", "path": "GoogleDataTransport/GDTCORLibrary/Public/GoogleDataTransport/GDTCORConsoleLogger.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98f24f601c9caf8749d2267eb49f741bcb", "path": "GoogleDataTransport/GDTCORLibrary/GDTCORConsoleLogger.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98de25cac333b5df5051fd3b79fb1e6d45", "path": "GoogleDataTransport/GDTCORLibrary/Internal/GDTCORDirectorySizeTracker.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98590b133e6bae88139fb0889c2b8912b6", "path": "GoogleDataTransport/GDTCORLibrary/GDTCORDirectorySizeTracker.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98c09d060b36dc073a281442915ceb5f47", "path": "GoogleDataTransport/GDTCORLibrary/Public/GoogleDataTransport/GDTCOREndpoints.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9800824d357fa3bff6455eca59e15bdb43", "path": "GoogleDataTransport/GDTCORLibrary/GDTCOREndpoints.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98ef83bb7238c9afa706bd243cd0d3c7ef", "path": "GoogleDataTransport/GDTCORLibrary/Private/GDTCOREndpoints_Private.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98a7844431923d0ff24e0a99398fdadf96", "path": "GoogleDataTransport/GDTCORLibrary/Public/GoogleDataTransport/GDTCOREvent.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98b6e850a8bbf6eb29fca437bd772f47d9", "path": "GoogleDataTransport/GDTCORLibrary/GDTCOREvent.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9855cf4ae456325d83afe7be5dc0922232", "path": "GoogleDataTransport/GDTCCTLibrary/Public/GDTCOREvent+GDTCCTSupport.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98faa366d04760315b24cdc1188faa15b5", "path": "GoogleDataTransport/GDTCCTLibrary/GDTCOREvent+GDTCCTSupport.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9849f800e325a9f71c098697b625a27c9a", "path": "GoogleDataTransport/GDTCCTLibrary/Private/GDTCOREvent+GDTMetricsSupport.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98b1d3bb238a9a470f3d9906fe4ec9d27d", "path": "GoogleDataTransport/GDTCCTLibrary/GDTCOREvent+GDTMetricsSupport.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98b502a83b5a59eb00f4c51b68067d7fb5", "path": "GoogleDataTransport/GDTCORLibrary/Private/GDTCOREvent_Private.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98c09d57cd02d4895fe2153ac9b1b40500", "path": "GoogleDataTransport/GDTCORLibrary/Public/GoogleDataTransport/GDTCOREventDataObject.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e987530d81db1c444ca1241c6e57c493380", "path": "GoogleDataTransport/GDTCORLibrary/Internal/GDTCOREventDropReason.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98f87f18c3a3c5840541baf20dca21d11d", "path": "GoogleDataTransport/GDTCORLibrary/Public/GoogleDataTransport/GDTCOREventTransformer.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e982eda11d55f7c182f4cfb51e29675808a", "path": "GoogleDataTransport/GDTCORLibrary/Private/GDTCORFlatFileStorage.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e980cf0047527501757d4e154f8b58eb4ae", "path": "GoogleDataTransport/GDTCORLibrary/GDTCORFlatFileStorage.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e984e3ad5dd0116be86dc12bf84139d9269", "path": "GoogleDataTransport/GDTCORLibrary/Private/GDTCORFlatFileStorage+Promises.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98fa0dd78c5b38465e0681399ff028ab0f", "path": "GoogleDataTransport/GDTCORLibrary/GDTCORFlatFileStorage+Promises.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98396ea6b7551e38fb321dc924fc4bfa5d", "path": "GoogleDataTransport/GDTCORLibrary/Internal/GDTCORLifecycle.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98df28eb594813dfdfc23c4957b1c30ca1", "path": "GoogleDataTransport/GDTCORLibrary/GDTCORLifecycle.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98726a89becbfa9514e8c7c7657e89a992", "path": "GoogleDataTransport/GDTCORLibrary/Private/GDTCORLogSourceMetrics.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98047229f4c26f35d70b4324ba16cb735a", "path": "GoogleDataTransport/GDTCORLibrary/GDTCORLogSourceMetrics.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e988e356b01df5ddd47361d30eba42c9d57", "path": "GoogleDataTransport/GDTCORLibrary/Private/GDTCORMetrics.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9887e830bc87ed4c4e9ce544a6c9ac8b6b", "path": "GoogleDataTransport/GDTCORLibrary/GDTCORMetrics.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9826a2a83d960aa641e2d1531175f8a764", "path": "GoogleDataTransport/GDTCCTLibrary/Private/GDTCORMetrics+GDTCCTSupport.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98e6ef8478d200beee4e0ac5df9fe9bea6", "path": "GoogleDataTransport/GDTCCTLibrary/GDTCORMetrics+GDTCCTSupport.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98411e4f9876d61e15142e448f0618e92f", "path": "GoogleDataTransport/GDTCORLibrary/Private/GDTCORMetricsController.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9837b3ff9c86addaa3affd180f47e9c90d", "path": "GoogleDataTransport/GDTCORLibrary/GDTCORMetricsController.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98e21f115eee365f4027a994722d8d7cc6", "path": "GoogleDataTransport/GDTCORLibrary/Internal/GDTCORMetricsControllerProtocol.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9875472df74567b9a75425ea2553244504", "path": "GoogleDataTransport/GDTCORLibrary/Private/GDTCORMetricsMetadata.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98075ac314e469ca7c83e8d4738b79a7e2", "path": "GoogleDataTransport/GDTCORLibrary/GDTCORMetricsMetadata.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98f0b39e0c6fafd4ae65c3b41b856b435d", "path": "GoogleDataTransport/GDTCORLibrary/Internal/GDTCORPlatform.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e987029346e7cb1395a56ae0141d8053583", "path": "GoogleDataTransport/GDTCORLibrary/GDTCORPlatform.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e982a468420b1784e4b5cf325ea0e6d8d71", "path": "GoogleDataTransport/GDTCORLibrary/Public/GoogleDataTransport/GDTCORProductData.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9859d84410ea96f6b8d83c31838dbd33d3", "path": "GoogleDataTransport/GDTCORLibrary/GDTCORProductData.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e985154af3a6385ad33690569144427e157", "path": "GoogleDataTransport/GDTCORLibrary/Internal/GDTCORReachability.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98c6064225fe4df2f835830089d5aa4695", "path": "GoogleDataTransport/GDTCORLibrary/GDTCORReachability.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98fa00b89769ece5ea909da11c7c789877", "path": "GoogleDataTransport/GDTCORLibrary/Private/GDTCORReachability_Private.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98d147add35c177e55bc41ea7b663c0b9c", "path": "GoogleDataTransport/GDTCORLibrary/Internal/GDTCORRegistrar.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98309cb05912ea9ca046273b30e96af64e", "path": "GoogleDataTransport/GDTCORLibrary/GDTCORRegistrar.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98a69bf8b17f48afdd1c9e18d5655fcf0f", "path": "GoogleDataTransport/GDTCORLibrary/Private/GDTCORRegistrar_Private.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98bd7406751a12be514d170ec37da5c1fd", "path": "GoogleDataTransport/GDTCORLibrary/Internal/GDTCORStorageEventSelector.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98dd017267813726943e9734bd99156278", "path": "GoogleDataTransport/GDTCORLibrary/GDTCORStorageEventSelector.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e989773811b86f8325f7137284255505985", "path": "GoogleDataTransport/GDTCORLibrary/Private/GDTCORStorageMetadata.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98fd5985f1117973e11c527066891571d5", "path": "GoogleDataTransport/GDTCORLibrary/GDTCORStorageMetadata.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9864c9e143a2810ad315d7ed86426cd8f3", "path": "GoogleDataTransport/GDTCORLibrary/Internal/GDTCORStorageProtocol.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e980474e0409ac5f712293e6c0dea8c232d", "path": "GoogleDataTransport/GDTCORLibrary/Internal/GDTCORStorageSizeBytes.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9826e2454fb605a63f5830dae255fc3ef4", "path": "GoogleDataTransport/GDTCORLibrary/Public/GoogleDataTransport/GDTCORTargets.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98fc74faae491c9bc57a5741dabf701a41", "path": "GoogleDataTransport/GDTCORLibrary/Private/GDTCORTransformer.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9805930803b665676a7c5938ff86baab3f", "path": "GoogleDataTransport/GDTCORLibrary/GDTCORTransformer.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9876a90613a81fb16a4c915264beaca891", "path": "GoogleDataTransport/GDTCORLibrary/Private/GDTCORTransformer_Private.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e986a3a3907e0c91bce69f41bc093ac1026", "path": "GoogleDataTransport/GDTCORLibrary/Public/GoogleDataTransport/GDTCORTransport.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98d6481c00beb9abf80a3f07a7160c347f", "path": "GoogleDataTransport/GDTCORLibrary/GDTCORTransport.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e982641b6645ea7ac182ca8980de6fe9b48", "path": "GoogleDataTransport/GDTCORLibrary/Private/GDTCORTransport_Private.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e983456d4408727873f04b015ac36c8ed2b", "path": "GoogleDataTransport/GDTCORLibrary/Private/GDTCORUploadBatch.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98b472a64124fd75ad5cafc4556637c127", "path": "GoogleDataTransport/GDTCORLibrary/GDTCORUploadBatch.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9839d8cdfa30557b842f7a243d0ba7ebc3", "path": "GoogleDataTransport/GDTCORLibrary/Private/GDTCORUploadCoordinator.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e989cb52d100d2bd49ba7cdaa1c0e138752", "path": "GoogleDataTransport/GDTCORLibrary/GDTCORUploadCoordinator.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98273e65716c5fa3ff7ba4940341d7d841", "path": "GoogleDataTransport/GDTCORLibrary/Internal/GDTCORUploader.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98841f53920ee986bc077f1677153adcf7", "path": "GoogleDataTransport/GDTCORLibrary/Public/GoogleDataTransport/GoogleDataTransport.h", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e98efee5342c1924501c64ab274a5b3ae77", "path": "GoogleDataTransport/Resources/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98a7f7093c779235019953001f985f64b9", "name": "Resources", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e98812bd5023c7e1aac038e40ed4d294cc7", "path": "GoogleDataTransport.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98bb095729b28987b0af4741155fe886e9", "path": "GoogleDataTransport-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e9840553d5688dace0234066674a81b4d20", "path": "GoogleDataTransport-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98b636f18d68644f61cce9f1d26d9f3b59", "path": "GoogleDataTransport-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98bdf4e0d421444e682a5c24b3c063371b", "path": "GoogleDataTransport.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98244baf78aacc92efd26609e9f3953e97", "path": "GoogleDataTransport.release.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98362009f722986c1a1eacf90481e8462a", "path": "ResourceBundle-GoogleDataTransport_Privacy-GoogleDataTransport-Info.plist", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e987679e566977de9a66c6bf261572b6688", "name": "Support Files", "path": "../Target Support Files/GoogleDataTransport", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98385201a74bcb59b94083e95aacd19f76", "name": "GoogleDataTransport", "path": "GoogleDataTransport", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9867c841464fe758dd104949269f7fdd04", "path": "Base/Sources/GMSEmpty.h", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "wrapper.xcframework", "guid": "bfdfe7dc352907fc980b868725387e98c2e2ed89aac00f8fd63fcf43f8fdf6cf", "path": "Base/Frameworks/GoogleMapsBase.xcframework", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98d43b93971b191d8a61e57edfafe8fe57", "name": "Frameworks", "path": "", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e984f59ceb815f39ff1efa2388b7afbe2c2", "name": "Base", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98b87fa60fa3f81b5e927501a47c0ac519", "path": "Maps/Sources/GMSEmpty.h", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "wrapper.xcframework", "guid": "bfdfe7dc352907fc980b868725387e989f5a61233c78bae4a8470b494fe37304", "path": "Maps/Frameworks/GoogleMaps.xcframework", "sourceTree": "<group>", "type": "file"}, {"fileType": "wrapper.xcframework", "guid": "bfdfe7dc352907fc980b868725387e986caacb9264bc4ddc9d18a7874e3f36cf", "path": "Maps/Frameworks/GoogleMapsCore.xcframework", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9873847af185ca53ba02cf15b64fcf9429", "name": "Frameworks", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "wrapper.plug-in", "guid": "bfdfe7dc352907fc980b868725387e9863de4292fb93e257b76ac1ed819fb097", "path": "Maps/Resources/GoogleMapsResources/GoogleMaps.bundle", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e989ac0c90b8b0acf7a5fbd0cbf32274c38", "name": "Resources", "path": "", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98f5f6eaad098cc9b2d4134f087de8f2ad", "name": "Maps", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.script.sh", "guid": "bfdfe7dc352907fc980b868725387e98cb6efc9839f6366889ae29dc33601173", "path": "GoogleMaps-xcframeworks.sh", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98d333c8cc190dd7bcd865fb83f92ca055", "path": "GoogleMaps.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e989d9efff39b2a812f075da693f1cbd4da", "path": "GoogleMaps.release.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98563f9125e74c6b62d7af9d301a9ac61e", "path": "ResourceBundle-GoogleMapsResources-GoogleMaps-Info.plist", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9880e1e8921a2ee1a66cdcfe4a576e8ff7", "name": "Support Files", "path": "../Target Support Files/GoogleMaps", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e985cdab478bdd43e0ed05116e7faa88537", "name": "GoogleMaps", "path": "GoogleMaps", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"fileType": "wrapper.xcframework", "guid": "bfdfe7dc352907fc980b868725387e98bb3056cf89453bf086d0b8728b790000", "path": "Frameworks/Release/UserMessagingPlatform.xcframework", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98357523f066799ab3070186e793ac171c", "name": "Frameworks", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e985e509858c9ae4819adc7f66778add1e0", "path": "Frameworks/Release/UserMessagingPlatform.xcframework/ios-arm64/UserMessagingPlatform.framework/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9884572151143ddfc0d0f241fffa065bb0", "name": "Resources", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.script.sh", "guid": "bfdfe7dc352907fc980b868725387e98181967d8483517ec328eebdf9e7a86c3", "path": "GoogleUserMessagingPlatform-xcframeworks.sh", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e981c18e9f83dc518811c0e14630ee3ae23", "path": "GoogleUserMessagingPlatform.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e989e22d7522cffa874ffd75f3f1a185520", "path": "GoogleUserMessagingPlatform.release.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e982e450f3baaabceb642193a07affb27f3", "path": "ResourceBundle-UserMessagingPlatformResources-GoogleUserMessagingPlatform-Info.plist", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e982cc5d16667c681b210101ad3eab43431", "name": "Support Files", "path": "../Target Support Files/GoogleUserMessagingPlatform", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e981953a9bdd698d1f3d8e50176ca1ddace", "name": "GoogleUserMessagingPlatform", "path": "GoogleUserMessagingPlatform", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e988643d0871830a778658caee60f3c1d38", "path": "GoogleUtilities/AppDelegateSwizzler/Public/GoogleUtilities/GULAppDelegateSwizzler.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98708d6cd4dca8a1a7c34e8f52533a77fb", "path": "GoogleUtilities/AppDelegateSwizzler/GULAppDelegateSwizzler.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9895161e55c799822f968accfb85da88b0", "path": "GoogleUtilities/AppDelegateSwizzler/Internal/GULAppDelegateSwizzler_Private.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98b3e9f6bd81db8e3de0212a2d9fff215d", "path": "GoogleUtilities/AppDelegateSwizzler/Public/GoogleUtilities/GULApplication.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e981e8e29fcfe01772983b6db0ecefc4697", "path": "GoogleUtilities/Common/GULLoggerCodes.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e987182c5362811ea5ee52408526da39bbf", "path": "GoogleUtilities/AppDelegateSwizzler/Public/GoogleUtilities/GULSceneDelegateSwizzler.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e983ee67f5fb881734dd6e34fbadd1b7b8c", "path": "GoogleUtilities/AppDelegateSwizzler/GULSceneDelegateSwizzler.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98cf7901f8031ed816529c38c086cb9db4", "path": "GoogleUtilities/AppDelegateSwizzler/Internal/GULSceneDelegateSwizzler_Private.h", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98e0546da75189786ab22551e1c88bb552", "name": "AppDelegateSwizzler", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98c35ccf2f1c53f37b4ab7bdc34f4c65a0", "path": "GoogleUtilities/Environment/Public/GoogleUtilities/GULAppEnvironmentUtil.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98663943a01e59c5a892a1336c549417ab", "path": "GoogleUtilities/Environment/GULAppEnvironmentUtil.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e987a3b1ce51d1aea409768f958a6c1ca9e", "path": "GoogleUtilities/Environment/Public/GoogleUtilities/GULKeychainStorage.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9855e3c0b00ffa31a7c83bb7516d0ed913", "path": "GoogleUtilities/Environment/SecureStorage/GULKeychainStorage.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98f5f2a6375d635390a18d20566b2577c4", "path": "GoogleUtilities/Environment/Public/GoogleUtilities/GULKeychainUtils.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e986aa2ae3bf664317873d8fdfd8644baf7", "path": "GoogleUtilities/Environment/SecureStorage/GULKeychainUtils.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9821403025c6c43088dc9af77d32e68860", "path": "GoogleUtilities/Environment/Public/GoogleUtilities/GULNetworkInfo.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98362339b1e60a7d3830a7169b624c82d1", "path": "GoogleUtilities/Environment/NetworkInfo/GULNetworkInfo.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e987e7891157f6510fca722583ab97b0bba", "path": "third_party/IsAppEncrypted/Public/IsAppEncrypted.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9894867a791f6055f28faf5731a0bfa745", "path": "third_party/IsAppEncrypted/IsAppEncrypted.m", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98738b5cd123f51c8e91b03ced175c7282", "name": "Environment", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9839f367bd307aa3d8b73b282554c46dbb", "path": "GoogleUtilities/Logger/Public/GoogleUtilities/GULLogger.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98364a8cb9ae090d57d28f9d0ef658b8c8", "path": "GoogleUtilities/Logger/GULLogger.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e984ecd2b2458a6c08d1d528e50ed4803bf", "path": "GoogleUtilities/Logger/Public/GoogleUtilities/GULLoggerLevel.h", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98fd394e774f85cb3057bb56d828569928", "name": "<PERSON><PERSON>", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98034e57ee00836aaca14a7d1d800f3d30", "path": "GoogleUtilities/Network/Public/GoogleUtilities/GULMutableDictionary.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98b7dda4280787caf5ed9bd3cd4a18a5e2", "path": "GoogleUtilities/Network/GULMutableDictionary.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98463e19899367fc1ce126ff9911ed94ab", "path": "GoogleUtilities/Network/Public/GoogleUtilities/GULNetwork.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e989e025a8467607cae3af8dd2b8e91ca20", "path": "GoogleUtilities/Network/GULNetwork.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e988acdf879abf6bf034208fcb935cceb07", "path": "GoogleUtilities/Network/Public/GoogleUtilities/GULNetworkConstants.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98b6e83564b39026215f5a5803ef74d295", "path": "GoogleUtilities/Network/GULNetworkConstants.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98b8364622e48d5192aeeaa32f914c1522", "path": "GoogleUtilities/Network/GULNetworkInternal.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9865b9f1848b7363c740a222ff91c11393", "path": "GoogleUtilities/Network/Public/GoogleUtilities/GULNetworkLoggerProtocol.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e982958a4f78c7f64d76dd29b9239ec6638", "path": "GoogleUtilities/Network/Public/GoogleUtilities/GULNetworkMessageCode.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98193214b5ed1fdba98c2e46f858478641", "path": "GoogleUtilities/Network/Public/GoogleUtilities/GULNetworkURLSession.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98d63b1477202368eca18b5deb3eb18848", "path": "GoogleUtilities/Network/GULNetworkURLSession.m", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98b8104e70c9883d47b79de66d63a3d517", "name": "Network", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98dbd3005f8719966c2cd9857b1c8c5453", "path": "GoogleUtilities/NSData+zlib/Public/GoogleUtilities/GULNSData+zlib.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98a50819d2eae3bd3a3896b186068a8961", "path": "GoogleUtilities/NSData+zlib/GULNSData+zlib.m", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e983a267301cb98aa0256d892662ab393b7", "name": "NSData+zlib", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e98ed6a9271d4bc121f57a50148444b1e2d", "path": "GoogleUtilities/Privacy/Resources/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e986c275ea2b24332fbd1777ab9c1e9f57d", "name": "Resources", "path": "", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9804e185fcf22f1863f194f2eb0bc313e6", "name": "Privacy", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e986e93c8a49ca7f0d724cbc781f23b3e02", "path": "GoogleUtilities/Reachability/Public/GoogleUtilities/GULReachabilityChecker.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98a357bd69b0a9399a75489743fe7437dc", "path": "GoogleUtilities/Reachability/GULReachabilityChecker.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9831a6214015468b5bae4cb0704bc40447", "path": "GoogleUtilities/Reachability/GULReachabilityChecker+Internal.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e985f46c9f5b034aab88c5a7ae77d72b7a4", "path": "GoogleUtilities/Reachability/GULReachabilityMessageCode.h", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e982fe0c30117e4d5c5d47830660569bc77", "name": "Reachability", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e9871ffcfc775e7e40c972a04fada01bb2a", "path": "GoogleUtilities.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98aca867ee2634d92e489065cfa0c44267", "path": "GoogleUtilities-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e988f3f98ed1974ca3f3d1af3c854fc24f9", "path": "GoogleUtilities-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98970ae0a067a74f8d50820b633f2b89b5", "path": "GoogleUtilities-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e985417a18c365faa60df0625e45de6fbe4", "path": "GoogleUtilities.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e983d8a1b5effda1cf7a94383f46d068c8b", "path": "GoogleUtilities.release.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98ead42c05fbbd7780e3b8118c429caf75", "path": "ResourceBundle-GoogleUtilities_Privacy-GoogleUtilities-Info.plist", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98407f7b1baad9cafed124f8732e46c535", "name": "Support Files", "path": "../Target Support Files/GoogleUtilities", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9819d5da13f65b989f8f963b96c73ad95f", "path": "GoogleUtilities/UserDefaults/Public/GoogleUtilities/GULUserDefaults.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98ee1baf432ffb9b830e6d32e0ba0c2975", "path": "GoogleUtilities/UserDefaults/GULUserDefaults.m", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e980d0fca929372d11f943fd8cb6252a7e6", "name": "UserDefaults", "path": "", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98a759c5357b8636c9d861e1570494fdfc", "name": "GoogleUtilities", "path": "GoogleUtilities", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98754e5dc59ceff3f3ac190f07a7a791ef", "path": "pb.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e98b42520d2de3f1b36025ad2b6c093c11f", "path": "pb_common.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e984e64243738bc47582dcfd92ec8cfe9c2", "path": "pb_common.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e98f7ec5d7be0e74570d547d47bb3d45bdb", "path": "pb_decode.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98d4c56fd9d2aafdc13ff291c80722caf7", "path": "pb_decode.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e98bf8627fc622d589de7af99714b16627d", "path": "pb_encode.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98ead602bb3aa01747752892f8a2fed19e", "path": "pb_encode.h", "sourceTree": "<group>", "type": "file"}, {"guid": "bfdfe7dc352907fc980b868725387e98008047aff0a9a59153cd3dcad0eb18ac", "name": "decode", "path": "", "sourceTree": "<group>", "type": "group"}, {"guid": "bfdfe7dc352907fc980b868725387e98c6eff1f2d7befc9e976a67774791a780", "name": "encode", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e98cbd680fe3f86c608edeafab24dacdc67", "path": "spm_resources/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98609a0ad7299d037413af8e29c94f9ea4", "name": "Resources", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e986473bec2bbaf66c3f0903d1dc4575400", "path": "nanopb.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98b8bcde0e4386f127dcfc90cf73b111a4", "path": "nanopb-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98b05685cb7e4db7dec9d578c65db149fc", "path": "nanopb-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98b3a1650f7db8c83e132fd6e3ad0f0757", "path": "nanopb-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98330d452f373c58d2bb224127c477f422", "path": "nanopb-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98a8fb843795737f14de0cd187c5536f3e", "path": "nanopb.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98e46a1e0edb81831eef604b36cf8f99eb", "path": "nanopb.release.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98f188ab3aa5cf2e17546745d450f94f3c", "path": "ResourceBundle-nanopb_Privacy-nanopb-Info.plist", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e982cdd11ebb405b76b3aa69ebaa252e572", "name": "Support Files", "path": "../Target Support Files/nanopb", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e989816ffe037342c33bcbe31ba594a2be4", "name": "nanopb", "path": "nanopb", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"fileType": "wrapper.xcframework", "guid": "bfdfe7dc352907fc980b868725387e9889457e4ddfd46c976f709b4d0aa9911e", "path": "OSMFlutterFramework.xcframework", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e984de357d2688494d5728a25c0252e4918", "name": "Frameworks", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.script.sh", "guid": "bfdfe7dc352907fc980b868725387e98030fc8385d448e7a15b7026b80cdf31d", "path": "OSMFlutterFramework-xcframeworks.sh", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e985f5f540e584c86ff4665f7be00b9120c", "path": "OSMFlutterFramework.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e983a9bd66d3c384e70e20ae6c849dd6fc6", "path": "OSMFlutterFramework.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98f0f5dbd572e2a636769a20e17fb5ad21", "name": "Support Files", "path": "../Target Support Files/OSMFlutterFramework", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e986d7793a2a670037caeec3a757a2f11fb", "name": "OSMFlutterFramework", "path": "OSMFlutterFramework", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98ef9fd6eb4375a775187ca3e358493d6c", "path": "Sources/Polyline/CoreLocation.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9832eb9ab9fa252e1083883ab5938a2107", "path": "Sources/Polyline/Polyline.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e984ca84dfcf73db8c9f0960ce059e286f5", "path": "Sources/Polyline/Polyline.swift", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e98ac99a965fb325e3b82ced8d846ef62bf", "path": "Polyline.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e988e72e1b9dbfa4e5ca870b5b07bb5ea41", "path": "Polyline-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98a586ac04fe62c6691587ad462907050c", "path": "Polyline-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9819a48d71944650ba219c4c77b7fc925a", "path": "Polyline-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98b4a1db8685f20fee7cfb2d88821e60bb", "path": "Polyline-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e983c618e457abdcf9408584f887bc8337b", "path": "Polyline.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98198e502cfcc58c6e8d3545966af3de48", "path": "Polyline.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98cc3690228c08652b32b0bf5295d879d4", "name": "Support Files", "path": "../Target Support Files/Polyline", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98daa453f29de0e8442cd30879e0b16a95", "name": "Polyline", "path": "Polyline", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e983adc6e23a5ba748b9c943b5d3a9f136b", "path": "Sources/FBLPromises/include/FBLPromise.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9814ae8603b154e8dc1c35523a1aae21f3", "path": "Sources/FBLPromises/FBLPromise.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98b8ee57d5cff36a634e63fdf6e4bd5a05", "path": "Sources/FBLPromises/include/FBLPromise+All.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e984b44dd7aadf9a1e8cea1d62598deecd7", "path": "Sources/FBLPromises/FBLPromise+All.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e980ab49806a3b16e096e492fce87887cd5", "path": "Sources/FBLPromises/include/FBLPromise+Always.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98e6a8e6df6b376e4ea8ee33d72e9a19ce", "path": "Sources/FBLPromises/FBLPromise+Always.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98ce5800d759d1931909018c161ce3a926", "path": "Sources/FBLPromises/include/FBLPromise+Any.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9894deec6a0df612a31b5ce6449a9d5aaf", "path": "Sources/FBLPromises/FBLPromise+Any.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98e1b761ad06a32437f9b8bda59d5b9443", "path": "Sources/FBLPromises/include/FBLPromise+Async.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98720714de2e0db897302b294ca08ccde4", "path": "Sources/FBLPromises/FBLPromise+Async.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98a864a21fb004301ae6350d29ad79e24c", "path": "Sources/FBLPromises/include/FBLPromise+Await.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98d3050e366b8a80dd9fb913101510e8ef", "path": "Sources/FBLPromises/FBLPromise+Await.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98d7b14ddcdb5b67a5ee22061745f3b004", "path": "Sources/FBLPromises/include/FBLPromise+Catch.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9803571f52d202315b2a94b580f84436c5", "path": "Sources/FBLPromises/FBLPromise+Catch.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9801f96cf36663ff878d8bd0193a5b579e", "path": "Sources/FBLPromises/include/FBLPromise+Delay.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9816d2cb87c350ffd2dfa66dfb105d56eb", "path": "Sources/FBLPromises/FBLPromise+Delay.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98b725f9d76fd8b8f43fdf7a56a3fbec28", "path": "Sources/FBLPromises/include/FBLPromise+Do.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e988b16602ba71d9bf6e6496417cbcf5033", "path": "Sources/FBLPromises/FBLPromise+Do.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98811193f95bac02037035314a4bdfeab7", "path": "Sources/FBLPromises/include/FBLPromise+Race.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e989621ea8106a4a04eb24415c006dc128f", "path": "Sources/FBLPromises/FBLPromise+Race.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e984c155709aaad8571d9ab69795e72d987", "path": "Sources/FBLPromises/include/FBLPromise+Recover.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98d9fb5f15ce987915b5c9df4218944f7a", "path": "Sources/FBLPromises/FBLPromise+Recover.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98694b3493dab7aab0f789dfaaf10a36b8", "path": "Sources/FBLPromises/include/FBLPromise+Reduce.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e988b37e36ae9190ef0c242faa32d53b44e", "path": "Sources/FBLPromises/FBLPromise+Reduce.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98963c9e7cafcc352b2f76a777ea35e4c0", "path": "Sources/FBLPromises/include/FBLPromise+Retry.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98f5927e170365e3159f7d56aa5ed1f7c2", "path": "Sources/FBLPromises/FBLPromise+Retry.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98eb02e8e3cb39ab4f63a3c8a7868e1408", "path": "Sources/FBLPromises/include/FBLPromise+Testing.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98ecc97f1801a7d475e3fb45c99a179803", "path": "Sources/FBLPromises/FBLPromise+Testing.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e987448f0c33f60703feefa8d99d45448e4", "path": "Sources/FBLPromises/include/FBLPromise+Then.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9883be47e86b6cc47dff13d5b31e5c9ad0", "path": "Sources/FBLPromises/FBLPromise+Then.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e987f776e448fbea1c8addcd871103c4f14", "path": "Sources/FBLPromises/include/FBLPromise+Timeout.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98d2a95a9cd1cf11c175fca3e73505892d", "path": "Sources/FBLPromises/FBLPromise+Timeout.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e980cf147a02f8ecba06d13cd7b6fec373f", "path": "Sources/FBLPromises/include/FBLPromise+Validate.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98ad82f48bb94713de3fc05b080ac81c06", "path": "Sources/FBLPromises/FBLPromise+Validate.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e987e1198c52d7a12e1c32bd51574bf4bcb", "path": "Sources/FBLPromises/include/FBLPromise+Wrap.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e988cd9927b2f5f9a89f4b27edf87cd29b3", "path": "Sources/FBLPromises/FBLPromise+Wrap.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e984be90e26dc8865dcc3ea5f9092aeb058", "path": "Sources/FBLPromises/include/FBLPromiseError.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e989177ad6eaac9b9d9672f09d2a4b19cf0", "path": "Sources/FBLPromises/FBLPromiseError.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9885f24ca394d0041109a70e4b18e26457", "path": "Sources/FBLPromises/include/FBLPromisePrivate.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9894c8f5b201917959d4f59fd1cdb8ea31", "path": "Sources/FBLPromises/include/FBLPromises.h", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e986c447f570e80b2881e2ca4be9ffdfa05", "path": "Sources/FBLPromises/Resources/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9822a70b0676d618f2253e356ac4b4c575", "name": "Resources", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e98f32d8d53fda86dd5af1637b3bb6213ac", "path": "PromisesObjC.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e985ff31a686a582de196175ea3bd815168", "path": "PromisesObjC-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98c48a093cbe29297c2baa1491be1e7882", "path": "PromisesObjC-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98352d2807168721f787cf27cb372fd5b6", "path": "PromisesObjC-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98a22c5ccbf0285c9d4d029bfcf737f5c1", "path": "PromisesObjC.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e9860b386ca9a6c361bcce2b17825120267", "path": "PromisesObjC.release.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98f2bba288f3cc3bfc8fdeb04a9cb85659", "path": "ResourceBundle-FBLPromises_Privacy-PromisesObjC-Info.plist", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9897a4e5dbef236dac77e2baa667eba93e", "name": "Support Files", "path": "../Target Support Files/PromisesObjC", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9849eb14b5ed69df7715896cb46144ef13", "name": "PromisesObjC", "path": "PromisesObjC", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9877cf2c091d0f3d020ca26ca79f245eec", "path": "SDWebImage/Private/NSBezierPath+SDRoundedCorners.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9850f33a6504d0d786a011921929cb0648", "path": "SDWebImage/Private/NSBezierPath+SDRoundedCorners.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9877b3856e5390720dbe0cd24e51c2f8c1", "path": "SDWebImage/Core/NSButton+WebCache.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98155a875a5851fc191e6240ab88fdf3c6", "path": "SDWebImage/Core/NSButton+WebCache.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e987e63ef23597d866028986c0967cc9771", "path": "SDWebImage/Core/NSData+ImageContentType.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9834db13bb029d012a787eab810fb7bef7", "path": "SDWebImage/Core/NSData+ImageContentType.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98187753f68bcb9ce6ada8ce41a29980da", "path": "SDWebImage/Core/NSImage+Compatibility.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98b66a38d797ed7327b67cf30261152803", "path": "SDWebImage/Core/NSImage+Compatibility.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e988dff5439c8a97dd72db6ef764aeff45f", "path": "SDWebImage/Core/SDAnimatedImage.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9867cd1a2f670dc7ba491c563409398c62", "path": "SDWebImage/Core/SDAnimatedImage.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e986f8c7504d6bfdf906a1c7f60f72e599f", "path": "SDWebImage/Core/SDAnimatedImagePlayer.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e985a8ef31d5ad8ffc2aff65984b0f2a961", "path": "SDWebImage/Core/SDAnimatedImagePlayer.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98e5a8c2ad0365ee1ee1c8683bd10f04fa", "path": "SDWebImage/Core/SDAnimatedImageRep.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9881e5ca3360096cfc4aa9ee94954c2cb4", "path": "SDWebImage/Core/SDAnimatedImageRep.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e987c9645c4c053a25fe3fbcfda9817c66b", "path": "SDWebImage/Core/SDAnimatedImageView.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98fa23570326102cd8aae66159447edcee", "path": "SDWebImage/Core/SDAnimatedImageView.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98b8f8a8e99a7893c39811892bec305183", "path": "SDWebImage/Core/SDAnimatedImageView+WebCache.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98aa85de623760b352c3908ff23bb43bdc", "path": "SDWebImage/Core/SDAnimatedImageView+WebCache.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98130c14faec70ca1de99783b2bb3cc6d6", "path": "SDWebImage/Private/SDAssociatedObject.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e981aad16e913e69b4abc1c55180b1d419a", "path": "SDWebImage/Private/SDAssociatedObject.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98572501dcee97ff2e992452c2e6c75c6a", "path": "SDWebImage/Private/SDAsyncBlockOperation.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98e6146f5835d75a0f5b53c79472da135b", "path": "SDWebImage/Private/SDAsyncBlockOperation.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e982ccf8e8eed239a96b1929c418c383e17", "path": "SDWebImage/Core/SDCallbackQueue.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98c85fcdbf23c6fbabdcd3e9beafe87ecb", "path": "SDWebImage/Core/SDCallbackQueue.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98bbca7df12807e48aa4b102224ea977c9", "path": "SDWebImage/Private/SDDeviceHelper.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98a18070bac4dd6186ece7b564da089f5a", "path": "SDWebImage/Private/SDDeviceHelper.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9824a812967699c09924014bc84109606f", "path": "SDWebImage/Core/SDDiskCache.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9881b1d3ec461f7b3acdd8aa910b79918d", "path": "SDWebImage/Core/SDDiskCache.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e986896da49d5583e77400e179d2ee2a373", "path": "SDWebImage/Private/SDDisplayLink.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9860505d65d7d508f9a4314f21b20d08d0", "path": "SDWebImage/Private/SDDisplayLink.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98fb8093728fcdbdd486633797caa7537e", "path": "SDWebImage/Private/SDFileAttributeHelper.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e983e274ac3bf813d65a71e19a7e592415b", "path": "SDWebImage/Private/SDFileAttributeHelper.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e986767d768cb0ade3f4f10d54cb518800d", "path": "SDWebImage/Core/SDGraphicsImageRenderer.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98894fab631628810974854bde0fb69560", "path": "SDWebImage/Core/SDGraphicsImageRenderer.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98a6cc70422a619e51405a979d11746934", "path": "SDWebImage/Core/SDImageAPNGCoder.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9836ae6ac53e4ab4175be66ea255fe5366", "path": "SDWebImage/Core/SDImageAPNGCoder.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98f6335f2eb27d2213fc3767773e4ef003", "path": "SDWebImage/Private/SDImageAssetManager.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e980b158b7b30263ac24c2f5932af3e9cb2", "path": "SDWebImage/Private/SDImageAssetManager.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e982f942736351cf08bb2512cbf77f11000", "path": "SDWebImage/Core/SDImageAWebPCoder.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e981f553e9341304353dc79d611ce87bb8a", "path": "SDWebImage/Core/SDImageAWebPCoder.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9865db9173552edd473231cb4b2d695ad6", "path": "SDWebImage/Core/SDImageCache.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9866a08aa6232d21acf1455130a1972f2c", "path": "SDWebImage/Core/SDImageCache.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9828bb36fb6a8aae6f437404d90d4ec09e", "path": "SDWebImage/Core/SDImageCacheConfig.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98c0a0152ec78fff1870a0b1b5f8f57214", "path": "SDWebImage/Core/SDImageCacheConfig.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e986ad8154f2bf364f606fc35f0c0acf8a1", "path": "SDWebImage/Core/SDImageCacheDefine.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98c16684e239222b62b52539fa9bedd990", "path": "SDWebImage/Core/SDImageCacheDefine.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98c13791ec0fe75e75484a0dd13748283e", "path": "SDWebImage/Core/SDImageCachesManager.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e988f032588026570a32e3da6a766766ed6", "path": "SDWebImage/Core/SDImageCachesManager.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98e483abbd66923ad3a85f072c5f2a99d6", "path": "SDWebImage/Private/SDImageCachesManagerOperation.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98ecc839907e7e2849fa8552457101e50f", "path": "SDWebImage/Private/SDImageCachesManagerOperation.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98e219659f152f7b83d37c1f117ad34a57", "path": "SDWebImage/Core/SDImageCoder.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98efbe366dacc2b6e9fe1a9c41783f0c5e", "path": "SDWebImage/Core/SDImageCoder.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98d6120ef709cddfc157698f5a9b8269aa", "path": "SDWebImage/Core/SDImageCoderHelper.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98cd62eefc8a6b05eefb9a8b3bac59f616", "path": "SDWebImage/Core/SDImageCoderHelper.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e981f66250a4ecaabfd38620d0ce1663f63", "path": "SDWebImage/Core/SDImageCodersManager.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98772123fc472c7846c7e69c2e55aec5bc", "path": "SDWebImage/Core/SDImageCodersManager.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9820bf26e3732689da860dafb0ad0af9a2", "path": "SDWebImage/Core/SDImageFrame.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e989b0a9803095f535b4e24694e032cea5a", "path": "SDWebImage/Core/SDImageFrame.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98edec0dcd8fb22a0c90767d47f0afbdfd", "path": "SDWebImage/Private/SDImageFramePool.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98b60f031d585de69f00e808520b70921e", "path": "SDWebImage/Private/SDImageFramePool.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98433e5f6cc5cfdaae2a5f9cacb78aab81", "path": "SDWebImage/Core/SDImageGIFCoder.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98a3c7d09b6fcfdb99cf4fd4a5c43463ed", "path": "SDWebImage/Core/SDImageGIFCoder.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e985647a975c39a97d4273dc5b244d2d3d1", "path": "SDWebImage/Core/SDImageGraphics.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9848ce6117f197255ea730db05b116e3be", "path": "SDWebImage/Core/SDImageGraphics.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98d1cf1a0d833627393bfacdc89ef0d4a0", "path": "SDWebImage/Core/SDImageHEICCoder.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98d80f6735a17f08a6a7406dcd6ba74c91", "path": "SDWebImage/Core/SDImageHEICCoder.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98cf933071df3854f32fd8e79a5e7048d6", "path": "SDWebImage/Core/SDImageIOAnimatedCoder.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98b394faf6a75cae131cb4a540673a9bb3", "path": "SDWebImage/Core/SDImageIOAnimatedCoder.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9855251e8fa6cefde629e82b0f44febf32", "path": "SDWebImage/Private/SDImageIOAnimatedCoderInternal.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98ba7a5b7de0583317b9441fe4ad4f2017", "path": "SDWebImage/Core/SDImageIOCoder.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e988e75f9d15a2e6a0401ab2bf0f51af05f", "path": "SDWebImage/Core/SDImageIOCoder.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e986f4d4ad7de7e1e357581a32102cee9b3", "path": "SDWebImage/Core/SDImageLoader.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e984dec30262ddc2ac7d0adf4028d1cf744", "path": "SDWebImage/Core/SDImageLoader.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98b25d2b3041c46870aa91d8d963743289", "path": "SDWebImage/Core/SDImageLoadersManager.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e980943f1b15ac870ba4f78a1df9b000714", "path": "SDWebImage/Core/SDImageLoadersManager.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98a639105a69f7a6844537dab7080b011b", "path": "SDWebImage/Core/SDImageTransformer.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9874045a7bf9842f5eadfcb05f13c871f7", "path": "SDWebImage/Core/SDImageTransformer.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e983b82b1fdf88d6595d1d9fca29f65781d", "path": "SDWebImage/Private/SDInternalMacros.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98d22116abf90228473caa0aef00426193", "path": "SDWebImage/Private/SDInternalMacros.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9882236c8aded47f5d1f1b52a211975004", "path": "SDWebImage/Core/SDMemoryCache.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9838a5b54f5b40b517b4f47bd6e512966f", "path": "SDWebImage/Core/SDMemoryCache.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9826387785b42df60ae698e86c916f6726", "path": "SDWebImage/Private/SDmetamacros.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98eb548dd8924e756e4010871966409a4f", "path": "SDWebImage/Private/SDWeakProxy.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98c2eae182648dfbd8dd5e302eed6d5fa2", "path": "SDWebImage/Private/SDWeakProxy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9807968a171fd0c799d5f708f7537a7618", "path": "WebImage/SDWebImage.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98069b4f72a83795828582281ee767ea7c", "path": "SDWebImage/Core/SDWebImageCacheKeyFilter.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9801b845427f28a8ef3b21b35c4bad1bf1", "path": "SDWebImage/Core/SDWebImageCacheKeyFilter.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98a5d898ee9b7736fa0422f7aed3d058e7", "path": "SDWebImage/Core/SDWebImageCacheSerializer.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e989b3f73537af7a811bd0feeb89b4766d4", "path": "SDWebImage/Core/SDWebImageCacheSerializer.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98be1825783d5cf7e792bb28414e3b7c22", "path": "SDWebImage/Core/SDWebImageCompat.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e989e06a886f579510a8e3e5577e8001683", "path": "SDWebImage/Core/SDWebImageCompat.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e981023b6fa7e8f949fcb2e3c95ad497868", "path": "SDWebImage/Core/SDWebImageDefine.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e981aa42cbb1330eb48c45d9e801f45f5ff", "path": "SDWebImage/Core/SDWebImageDefine.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9883f448ee3e43d88efa5c4de9181d917c", "path": "SDWebImage/Core/SDWebImageDownloader.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e983f9d1ba536b012d21ffe848cb2387c39", "path": "SDWebImage/Core/SDWebImageDownloader.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98bd1157828c1769cad7b9a851d3f93885", "path": "SDWebImage/Core/SDWebImageDownloaderConfig.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e986bb355b15a9358184a09769bf4d838fc", "path": "SDWebImage/Core/SDWebImageDownloaderConfig.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9882826dc7aadae5696a960be9e79d4796", "path": "SDWebImage/Core/SDWebImageDownloaderDecryptor.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98edf34d897ae0bdea997071d0dfde0929", "path": "SDWebImage/Core/SDWebImageDownloaderDecryptor.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98067f6959c8e3a798bd0078e5f679f46c", "path": "SDWebImage/Core/SDWebImageDownloaderOperation.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98cf1ad4511f004b4f8f03a7c983ece0bd", "path": "SDWebImage/Core/SDWebImageDownloaderOperation.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9820cfdbe5bf27c7a084bb04d389b5fed0", "path": "SDWebImage/Core/SDWebImageDownloaderRequestModifier.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e987f91ba3842631eda3808b3ccabaf7849", "path": "SDWebImage/Core/SDWebImageDownloaderRequestModifier.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98618479db3f76d43451f28c2b3a45c445", "path": "SDWebImage/Core/SDWebImageDownloaderResponseModifier.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e980d731e355e8d0e830d8e1b2451143b5f", "path": "SDWebImage/Core/SDWebImageDownloaderResponseModifier.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98dae9536a2f93cfa8e540294f9f376fbb", "path": "SDWebImage/Core/SDWebImageError.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98a67cdf7f403ef3b17e1665484c33e31f", "path": "SDWebImage/Core/SDWebImageError.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98136e6149c7eb7b2e2a7ff5b745b94575", "path": "SDWebImage/Core/SDWebImageIndicator.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98b10f96df58151781a255c20ed38b53e1", "path": "SDWebImage/Core/SDWebImageIndicator.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98f1c9c4066fbfab7441b68a350b601f90", "path": "SDWebImage/Core/SDWebImageManager.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9896c0abf92eb2865893a5f4b53fd43925", "path": "SDWebImage/Core/SDWebImageManager.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9817226ce784b273ec92c735bb4ec85f00", "path": "SDWebImage/Core/SDWebImageOperation.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e984c397377008406132401e6db1a4e7743", "path": "SDWebImage/Core/SDWebImageOperation.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9829955487e0791cdb1e0e13ca859816d4", "path": "SDWebImage/Core/SDWebImageOptionsProcessor.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98c0ce73e3802bf5d7e26801ad118a67ee", "path": "SDWebImage/Core/SDWebImageOptionsProcessor.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9868875a7cb4fddfc4e46e3e74380561dc", "path": "SDWebImage/Core/SDWebImagePrefetcher.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e980c4ddf3b02a1d4003ac73cb32cc82422", "path": "SDWebImage/Core/SDWebImagePrefetcher.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9859992c5b17e01027d46abe6734ad4f12", "path": "SDWebImage/Core/SDWebImageTransition.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e983df11ba392d6629192ad0adfdef26e05", "path": "SDWebImage/Core/SDWebImageTransition.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e982bd172df556541f4fa832bfa5b5db3fd", "path": "SDWebImage/Private/SDWebImageTransitionInternal.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9888993549342eb96a6eca0a99a9310058", "path": "SDWebImage/Core/UIButton+WebCache.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98275615051fe963d0f558d17407def65b", "path": "SDWebImage/Core/UIButton+WebCache.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98247e6af793538e41517cd3690631ef19", "path": "SDWebImage/Private/UIColor+SDHexString.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9854cb3274269ae40e8bcbe12a5e795664", "path": "SDWebImage/Private/UIColor+SDHexString.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e981a964cbd213a11c6471e648f4a45f328", "path": "SDWebImage/Core/UIImage+ExtendedCacheData.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9826b051b897d6c17808257e131ed8f215", "path": "SDWebImage/Core/UIImage+ExtendedCacheData.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98577062d8d1da3b4f7d05a85125208c18", "path": "SDWebImage/Core/UIImage+ForceDecode.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9817f0d984214970250b963ba967e568ae", "path": "SDWebImage/Core/UIImage+ForceDecode.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e984fbe8277ac59d9732ec5586b3fa192a0", "path": "SDWebImage/Core/UIImage+GIF.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9878b146fa2ce6e495142e1ef40b523433", "path": "SDWebImage/Core/UIImage+GIF.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9856a3e932df1fe965a8230516aa75f8af", "path": "SDWebImage/Core/UIImage+MemoryCacheCost.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98d8b9539a248a674432c05d3d5c9d68fa", "path": "SDWebImage/Core/UIImage+MemoryCacheCost.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9804f9670367ddec3d9c070e9a1f1d5774", "path": "SDWebImage/Core/UIImage+Metadata.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98e4364e4b588900f48e3dad4f0f8b2c3d", "path": "SDWebImage/Core/UIImage+Metadata.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98a556a64f20f386d3a125ee924b704e17", "path": "SDWebImage/Core/UIImage+MultiFormat.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98454a062811904af322cf3ede563c7382", "path": "SDWebImage/Core/UIImage+MultiFormat.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9838fabed8fe42f5a957d5c4b0af857617", "path": "SDWebImage/Core/UIImage+Transform.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e980c983226edb7c68af54c60708e44370e", "path": "SDWebImage/Core/UIImage+Transform.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e986ba2e31a0d29e158bb43bc349109f926", "path": "SDWebImage/Core/UIImageView+HighlightedWebCache.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98f560c015fa3fa8274e6ad607fd419cb7", "path": "SDWebImage/Core/UIImageView+HighlightedWebCache.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98ad2a690f52d15537cadd8ca84479e550", "path": "SDWebImage/Core/UIImageView+WebCache.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9865719a3006c4f2a35f3f34cba0e9280b", "path": "SDWebImage/Core/UIImageView+WebCache.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98f2a71e27ed07c2e57e7bd3df27ea15f0", "path": "SDWebImage/Core/UIView+WebCache.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98db64653118681af5863143943f463c44", "path": "SDWebImage/Core/UIView+WebCache.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9846e8159488b0dee390091a76fa0c601c", "path": "SDWebImage/Core/UIView+WebCacheOperation.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e982fe7e540d6c8410bff6b19cb93c96c23", "path": "SDWebImage/Core/UIView+WebCacheOperation.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98d83fa6c839a42968f82d9566d4ee354b", "path": "SDWebImage/Core/UIView+WebCacheState.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e984736e3cd9b10013136fe10843188d858", "path": "SDWebImage/Core/UIView+WebCacheState.m", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e9899d92cf72e45bbbedfe0e7ac3e1e6f94", "path": "WebImage/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e986a5cb1a74b676b7f866eb89681c60e7d", "name": "Resources", "path": "", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9893ba4477416ad03dc7798c005ad0a7bb", "name": "Core", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98918c70b4c26afe7b03c737863192a930", "path": "ResourceBundle-SDWebImage-SDWebImage-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e98ea64b01b95e0fe2cb02139d48e761f66", "path": "SDWebImage.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98c193562cbbcc86515ee863bfa1627dd1", "path": "SDWebImage-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e987b20280eff36fdac8ad27f4763f46544", "path": "SDWebImage-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9857a5ed82ffbd7a7b3875ca0bff3d4486", "path": "SDWebImage-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98882fb87722d79fe327623ea2c5435e11", "path": "SDWebImage-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e9850dc7d337511752f8b5c0df029050672", "path": "SDWebImage.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98f12f6ddd66afe9350f22ab0e6505c763", "path": "SDWebImage.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98ef0e2d7c50cb6edb112b6f24d0416b90", "name": "Support Files", "path": "../Target Support Files/SDWebImage", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9863ce3b0f79fa6b24c76807c223e95a95", "name": "SDWebImage", "path": "SDWebImage", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9886bc76869556b6d21e50d5cc0e6e3d75", "path": "SwiftyGif/NSImage+SwiftyGif.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9873b3af8235c992982adc01d49742e3ce", "path": "SwiftyGif/NSImageView+SwiftyGif.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e987d611d25a426c10bdb41b83dbd03c1bc", "path": "SwiftyGif/ObjcAssociatedWeakObject.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98c6bf2a9aabcb69660fa27766391d7c29", "path": "SwiftyGif/SwiftyGif.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e984a7508af8c363a2aad9fd7ab8cef5645", "path": "SwiftyGif/SwiftyGifManager.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e984590545918432f761ba63d02ee887523", "path": "SwiftyGif/UIImage+SwiftyGif.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98dc593a0c5cf08f314c83f1e03deb7aef", "path": "SwiftyGif/UIImageView+SwiftyGif.swift", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e98d6d69752824c094053433c9d5098210b", "path": "SwiftyGif/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e981ab1c0ac5dd30373d25875128a036ec4", "name": "Resources", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98988940e431669c2e7010210230cc04e1", "path": "ResourceBundle-SwiftyGif-SwiftyGif-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e9811405460b3b58ba6dcd61e328dd4abb4", "path": "SwiftyGif.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98be56628c09966c73350c6218247d342e", "path": "SwiftyGif-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e984475fe0ae2047865a0f14c1bc1b58bb6", "path": "SwiftyGif-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98f71b8087040102c943ad2d00e9ba758a", "path": "SwiftyGif-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9887ed944f1df79f63e0741c8332fc90bd", "path": "SwiftyGif-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e987966feeed4f42c5d5d39542995a2f3ae", "path": "SwiftyGif.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98fa81e78a107402ce5f4e9c6aa3fa7cdc", "path": "SwiftyGif.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98f125e564a8f447d23858b0e81f4eadaa", "name": "Support Files", "path": "../Target Support Files/SwiftyGif", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98fe0270fcfa8c2b2c53d201774d8298bc", "name": "SwiftyGif", "path": "SwiftyGif", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98213848b9b1c0e075a11446159579a3c6", "path": "Toast-Framework/Toast.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9862827b3d3084945c21ef0a1bee3e4580", "path": "Toast/UIView+Toast.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9857b9f3d84310d6372f0bd7b43eb8ef1f", "path": "Toast/UIView+Toast.m", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e98f33e3dcdf1c89e8c0f31742e12782715", "path": "Toast/Resources/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9802544c620f8a3cda916c6f4a33371c73", "name": "Resources", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e980d4ff9745392fd04b48de90d8d6fe28d", "path": "ResourceBundle-Toast-Toast-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e98d4587b74c6cb56ba555d53b47628551c", "path": "Toast.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98e29d618821800e0c9afd904693c3012c", "path": "Toast-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98d7eb28f8d5517039a462c8a08b4b8ef8", "path": "Toast-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e988dccc4bfa0e5bebf79c90babcfee4fc1", "path": "Toast-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98a955438230ff874d5acd7d6b7f22fc32", "path": "Toast-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98068dd8b63bd58348b80aebc24611c67f", "path": "Toast.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e980a918f19207ec577dc4a5a9949c00054", "path": "Toast.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98b742745473868e45669d048e3e9c131d", "name": "Support Files", "path": "../Target Support Files/Toast", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98f990b641fe297cb7cc847032a6ec2b29", "name": "Toast", "path": "Toast", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e98d7a9de58e63e33b18e3fe230e028d416", "path": "Sources/CYaml/src/api.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e984e6147c9feef1436791c4bb547266401", "path": "Sources/Yams/Constructor.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9806425ecdfef38f92676c526742414658", "path": "Sources/CYaml/include/CYaml.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98da4e74be948c67e7cc53d8ffd4b238ec", "path": "Sources/Yams/Decoder.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e98eed3650420fc9a7c41a53defad24c6a0", "path": "Sources/CYaml/src/emitter.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e980679d763bb151b0a087dd49d2a4b4abd", "path": "Sources/Yams/Emitter.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98da9a0dae448cc95acc5ae9d860f0126a", "path": "Sources/Yams/Encoder.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98c7cbbdce676cc1f9ffd65b1fb0a9a77c", "path": "Sources/Yams/Mark.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98db8845e781e87314d75e6091d70047bd", "path": "Sources/Yams/Node.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e988b2a9de2bf31f5d9a86b9e2d10aee2a4", "path": "Sources/Yams/Node.Mapping.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e983ddd18314d831a0f1d566242e8e92916", "path": "Sources/Yams/Node.Scalar.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98887d3fe133b1cdf275b93647c4a3d240", "path": "Sources/Yams/Node.Sequence.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e98adc4ce7eda9fd812ca6b45a4fa28f4a7", "path": "Sources/CYaml/src/parser.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98e366ec49b1d20d34606726362fb3d840", "path": "Sources/Yams/Parser.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e98d74d6f9364a8a59b6b316519924382b1", "path": "Sources/CYaml/src/reader.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98495075ece30d47c9b83728d0a060650f", "path": "Sources/Yams/Representer.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e988276f8a2d3b5d36c5b348b6a7f3d07c3", "path": "Sources/Yams/Resolver.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e98ffb980192edc242bdde8e412a27fdd61", "path": "Sources/CYaml/src/scanner.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e981de1123e5006fcac6bd69ced339c4c0b", "path": "Sources/Yams/String+Yams.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98a9e34c88ec71c5ac4cfea87189b5bdb6", "path": "Sources/Yams/Tag.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e980ce745c026b8d382ac429d37c655447e", "path": "Sources/CYaml/src/writer.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98a252c6befe4d62f1c9ebf6e17839ba77", "path": "Sources/CYaml/include/yaml.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9885e7b2dc719c7ce6d008372eeac40a28", "path": "Sources/CYaml/src/yaml_private.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98bcc3126e69003ce059bd4b92795805f5", "path": "Sources/Yams/YamlError.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9898291a82382243f4b1cbdd37c6b16b0f", "path": "Sources/Yams/Yams.h", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e98be5c5f34a347ee6219dee45bb2ec2e2c", "path": "Yams.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98b3dfefcd3b54b87012cb0ddf88eca001", "path": "Yams-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98d62e83e6c6a573640f0c8893e7f157e1", "path": "Yams-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e984547dd4a67ab0fa317854f0a39519fa0", "path": "Yams-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e989b69fa857406ff8ad3c4e2a925e78041", "path": "Yams-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e987b02f590fc0902ec529d16ef791e3c5c", "path": "Yams.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98d0de1a33dcee030010d798971a47ff76", "path": "Yams.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9889abc71d180cf027fa8a5edf798c94c4", "name": "Support Files", "path": "../Target Support Files/Yams", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e981293c7ce906512e0ecea2ae1560b3c90", "name": "Yams", "path": "Yams", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98adf8a50fb136fead426e57040a860333", "name": "Pods", "path": "", "sourceTree": "<group>", "type": "group"}, {"guid": "bfdfe7dc352907fc980b868725387e98818cdacfff6ada2429cbfc176568deb3", "name": "Products", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e98a3f4680c874ecbcb5b5a6338bc13a426", "path": "Pods-Runner.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e9814181933c973297b0001d0e9ee64381d", "path": "Pods-Runner-acknowledgements.markdown", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e9887440869a53d6020d968ddf6b9b30aa0", "path": "Pods-Runner-acknowledgements.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98fed0970e703d355c9aff813f280aafdc", "path": "Pods-Runner-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.script.sh", "guid": "bfdfe7dc352907fc980b868725387e9826e505869a67d81656859ded68178c1d", "path": "Pods-Runner-frameworks.sh", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98de02ac5005ab1eb052abf25c63731e95", "path": "Pods-Runner-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.script.sh", "guid": "bfdfe7dc352907fc980b868725387e98f6f4dcb116856f2da0fa97d3015cb1af", "path": "Pods-Runner-resources.sh", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e980ec6fbca7264a6936f2adfc48dd7f5bc", "path": "Pods-Runner-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e9850cc7fc2d23136fb4fac488d6c47df20", "path": "Pods-Runner.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98dc1a21852c7a085c7dd8f65cf0fa9907", "path": "Pods-Runner.profile.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98f17abf55d75f35efcaf45a1185b085b6", "path": "Pods-Runner.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98cf3c1972df678a5e36df75a46391500d", "name": "Pods-Runner", "path": "Target Support Files/Pods-Runner", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e984c4f55ec853c945e234980557a98aed8", "path": "Pods-RunnerTests.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e98fc0f7e7242f459f81e455145932dcafd", "path": "Pods-RunnerTests-acknowledgements.markdown", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e985f8b68b152f46f18718da20c04e675cb", "path": "Pods-RunnerTests-acknowledgements.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98022654f1ff78dd844d694dba2439dab2", "path": "Pods-RunnerTests-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e989e5ad6b9a07953a12c7008a15bd9c99c", "path": "Pods-RunnerTests-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98e5e8bcdff29e5f8321be18f7989b4bc7", "path": "Pods-RunnerTests-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98144cd18850e477837c238075d5256ffe", "path": "Pods-RunnerTests.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e981b663a2c82f0220040296818ba53477e", "path": "Pods-RunnerTests.profile.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98965b92d39d30a7872295adc2841cd1b1", "path": "Pods-RunnerTests.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9859551a2ccb1df711861b574920cd49bf", "name": "Pods-RunnerTests", "path": "Target Support Files/Pods-RunnerTests", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98dafc421ff02609f2772b356038eb9849", "name": "Targets Support Files", "path": "", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98677e601b37074db53aff90e47c8f96d1", "name": "Pods", "path": "", "sourceTree": "<group>", "type": "group"}, "guid": "bfdfe7dc352907fc980b868725387e98", "path": "/Users/<USER>/StudioProjects/test_notifictions/ios/Pods/Pods.xcodeproj", "projectDirectory": "/Users/<USER>/StudioProjects/test_notifictions/ios/Pods", "targets": ["TARGET@v11_hash=ddacf416dc1e781f588fb30e35ee7b64", "TARGET@v11_hash=6ee2cad1e516106acd7a0f4543f8bf9d", "TARGET@v11_hash=c4f95be29edc89c02c5a05ecca46c523", "TARGET@v11_hash=1617efb8f59fb12d4cb8d37b953af892", "TARGET@v11_hash=aa55c45cdcdc1630ccafa5b665ff3945", "TARGET@v11_hash=09c7e2bf2f17350a63dd6a808219cbd8", "TARGET@v11_hash=2d7c94378164324b314de5a504feb7e9", "TARGET@v11_hash=fe7bb8af251ff8ef3b6a478f20269719", "TARGET@v11_hash=2e0f1a2765d80128613b263635ac5935", "TARGET@v11_hash=ad2715f8ed0e84496154f0617d0d655b", "TARGET@v11_hash=7352e10a28029645c255ee937ae499c6", "TARGET@v11_hash=8d705b43acc7eed0fec6ff3a6c0beede", "TARGET@v11_hash=63f5801b331014fa8608b0b77eb92b73", "TARGET@v11_hash=d8ce47e59e7917d13d3c9013718fe986", "TARGET@v11_hash=f9d97a8c06076de801ffe79ed610943f", "TARGET@v11_hash=707a542dceaecce8fca76eb0a31eddab", "TARGET@v11_hash=a65391872c714c61c40230dc750ada2a", "TARGET@v11_hash=1887b6ce2ad0dfe707f1a36cf216d7a9", "TARGET@v11_hash=7d483337c226299ef9705042513637ab", "TARGET@v11_hash=e2bf664876a4b6ba7431086de8633471", "TARGET@v11_hash=3f3d3cb4a9daf871ab75a766bea6ceb3", "TARGET@v11_hash=3e28d75e77f8269346d6bfcae01e34f3", "TARGET@v11_hash=609beb960b8a3714045f253d99e381d6", "TARGET@v11_hash=b66416c706ca1e990b97c7eca63d9971", "TARGET@v11_hash=d64c60902cae1cef617b657bac29f84e", "TARGET@v11_hash=f097f010bfe737d470ef678000068231", "TARGET@v11_hash=8b673b72f18381a4583eceda26bcad63", "TARGET@v11_hash=901350fd25abd21a6d0867d85bc1bc02", "TARGET@v11_hash=9c3d6189dc4b07828b9c2ff423d45df4", "TARGET@v11_hash=1208ca450b2b30cefb4892491a05bcd3", "TARGET@v11_hash=4bf01d8ddad5235764a1047bb0364243", "TARGET@v11_hash=195e010affd4bd270fc17465211c3af3", "TARGET@v11_hash=c3367ec05f8951699efe8bcbe244d5d0", "TARGET@v11_hash=28eb68c42b91f703534e311467cda882", "TARGET@v11_hash=2aac21c18cd0c94afb2adbef63c8f3e5", "TARGET@v11_hash=8b65a1f020601c4cdc4e70b5f43ed622", "TARGET@v11_hash=5111fb3b3255668ae0f4e1109711e04f", "TARGET@v11_hash=8fb15f051a2c9e793608a846ba660e1a", "TARGET@v11_hash=74ebe907841df743de74fc4ea831d7ad", "TARGET@v11_hash=d110daf8aac9f4d939efa06bea9f87a9", "TARGET@v11_hash=b810b67deb891f90d42891940a138901", "TARGET@v11_hash=8c73e55a41ae418bce3c64e971e4d500", "TARGET@v11_hash=24aeeea9d7dfce38b991d3dac1f06471", "TARGET@v11_hash=6a180807b4e0ce6993570d13c472ae2c", "TARGET@v11_hash=620a49f3387b191d967a1618248c52c5", "TARGET@v11_hash=8d5e63db74d1b795e1464090ae4c6b8b", "TARGET@v11_hash=dd2f3ecbeca4d7bf96e955d57f484290", "TARGET@v11_hash=e4ba3af1ffdd91ce22ccc39324385b59", "TARGET@v11_hash=6363fdf626d9cff79158c2900438f668", "TARGET@v11_hash=2727afdcf9810098ff21b1d3935007cb", "TARGET@v11_hash=20961d8227c4c90703c1147f28e4dc12", "TARGET@v11_hash=6004685549fb40f1ef48522722ba9340", "TARGET@v11_hash=4502d1741034fbf65b34802061d41d2f", "TARGET@v11_hash=008f19cd7eaae46bad12956fb65a87fc", "TARGET@v11_hash=ba7a3fdacf3eb7eeecc8d60e48570948", "TARGET@v11_hash=3e569e4469c8f9d4205b30344617a325", "TARGET@v11_hash=16ef8b71a7ce39dbc9f4ac609d21352e", "TARGET@v11_hash=15b352a348157f044902f53b6b603699", "TARGET@v11_hash=7229bdd3fd17694b154769ab2b7a45d3", "TARGET@v11_hash=37e3be778a1f90f09b462e9112dfabca", "TARGET@v11_hash=f548a56a4b248bf88ea93b2085ab58f4", "TARGET@v11_hash=7fb7e46a0a52042d3fc73b9ec3c1ae06", "TARGET@v11_hash=2eb4ebe5f27514e9d5c2ae1aff84a411", "TARGET@v11_hash=a28d5243ed52e13fc537f0cae134e91a", "TARGET@v11_hash=32251c88e2ad10b6233e464a25a3b3fb", "TARGET@v11_hash=ce8ba4528dcef4f02cbcebe8cad05b74", "TARGET@v11_hash=27cd8b97fe3d0cbb406e134fc4300587", "TARGET@v11_hash=714d8d7b5aab4976495d17b0b5bb8409", "TARGET@v11_hash=247c2cc730f2279dca329e1af76d61a7", "TARGET@v11_hash=ab9ae4f248d71aefc74a9b312f9032d3", "TARGET@v11_hash=f5657e1cee1686107d102adc3f54e3eb", "TARGET@v11_hash=a250a2076e86c5e65223d05082defb1d", "TARGET@v11_hash=f6f27b1b8802672f459b16f9cf9d699d", "TARGET@v11_hash=ebb7fd8554eff4c0fcb945d8796cc46f", "TARGET@v11_hash=3e91f0e50c4cdaf3dda330507fcf76c1", "TARGET@v11_hash=15693af4ad6e8de601e0f308f42ea3c7", "TARGET@v11_hash=2fed11927b73325aa9030c0922c12a28", "TARGET@v11_hash=edda9ec151b6a8567bb6c8b09dc79755", "TARGET@v11_hash=08e01fd490220c959ebd631a3eb3c021", "TARGET@v11_hash=926fc6ef5735eeaa783f0cfd0cb4cb7f", "TARGET@v11_hash=6c0e1782ff8db9427954886f16c5b3b4", "TARGET@v11_hash=08f37dd20490c7128768fea90ba94bb4", "TARGET@v11_hash=4ed4e3e75116c976cdd7477d9f0690e5", "TARGET@v11_hash=01420645e1f37b1f04812b14e4a5895d", "TARGET@v11_hash=1ec7c80e59ccd786680f4e9411007422"]}