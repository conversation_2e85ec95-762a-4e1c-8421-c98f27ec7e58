{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e986aac1c87bf5fe61887e35828eedba156", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGNING_ALLOWED": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/Developer/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/Developer/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/geolocator_apple/geolocator_apple-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/geolocator_apple/geolocator_apple-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/geolocator_apple/geolocator_apple.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "geolocator_apple", "PRODUCT_NAME": "geolocator_apple", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e984936d7cb8feef48513f6131e211caece", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e986fcd143f5e1832ba1b029745b4810aa7", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGNING_ALLOWED": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/Developer/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/Developer/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/geolocator_apple/geolocator_apple-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/geolocator_apple/geolocator_apple-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/geolocator_apple/geolocator_apple.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "geolocator_apple", "PRODUCT_NAME": "geolocator_apple", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98beaaf634ac9c45a28ea8b53ae8cea921", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e986fcd143f5e1832ba1b029745b4810aa7", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGNING_ALLOWED": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/Developer/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/Developer/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/geolocator_apple/geolocator_apple-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/geolocator_apple/geolocator_apple-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/geolocator_apple/geolocator_apple.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "geolocator_apple", "PRODUCT_NAME": "geolocator_apple", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9879f34f1e012c285b844492b857411475", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98e7f52caa1bc86db5c848612cd4284141", "guid": "bfdfe7dc352907fc980b868725387e980ef4dc7532c29cefddc3729f81249df4", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f94395b70cb90e18f273307a163ee033", "guid": "bfdfe7dc352907fc980b868725387e989acbdba2b4f57e37a82f82dfbc4b6a88", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987bc9e49cfe67f1915ca9b3ae1cdef94a", "guid": "bfdfe7dc352907fc980b868725387e98ccf33296c77afb7d83454b7cd831b969", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988e6f4169f9d3de5e95e1541ba6a6140e", "guid": "bfdfe7dc352907fc980b868725387e98c0e1142d20b85845ce4337738948533f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983d2088a8808d6b0a1dc4597bfefb34f9", "guid": "bfdfe7dc352907fc980b868725387e98b4d22474482daf76bebf900c4787a4ea", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9820a3b2be7f7f386f669c4003156ffd53", "guid": "bfdfe7dc352907fc980b868725387e9861c163610b2f649ece896910dcb5d3c0", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a8a095793c7b2f8c3ee6344218508440", "guid": "bfdfe7dc352907fc980b868725387e98827666d018d0cbfa3a29dffc405c37c2", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ec4728722983a85e944e496f537bfa43", "guid": "bfdfe7dc352907fc980b868725387e98638ce503f76de7fccf81ce51b3c8367d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98171cadbc4fbfb507c543d082ddc83415", "guid": "bfdfe7dc352907fc980b868725387e98a2bb664801eec5224376b31fb5cb0d4b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9825656976e65bf997d58ef8aa47109992", "guid": "bfdfe7dc352907fc980b868725387e98eded081ad84d32301f6cab36b236dbd5", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98deb5ae3ca48066844f28bdb50717254e", "guid": "bfdfe7dc352907fc980b868725387e9879609b44de9cccee41b189e59f22d9ef", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d5ef9d96003d869fa03a29122c22401a", "guid": "bfdfe7dc352907fc980b868725387e98d75cda691380849a16048fe56a59cc35", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dc8f329d50a1b846508e23634352dddb", "guid": "bfdfe7dc352907fc980b868725387e981f3fe2209d00e658a00989ce4051a1fb", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9895d6843462f2147e35c38e14b4f49c52", "guid": "bfdfe7dc352907fc980b868725387e981a0466bf65d5117c178f7f22ec631484", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980fd424781da721ba6e8e557d554a6dfe", "guid": "bfdfe7dc352907fc980b868725387e985d6cd2f9cd7473d0b0f972a846a20dfd", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9814d27b824a6a7d8e3a8ff4937c0f465a", "guid": "bfdfe7dc352907fc980b868725387e98bcacb351ee6587e77fb411198be3d146", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9823eaec64f5b0e20692ae409877677a4d", "guid": "bfdfe7dc352907fc980b868725387e98171180797fa255533f9f7994c8560a83", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e986576ae2c21c60200c6d4fa060d500570", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e989aa2723881855b7875f9d403f7104afb", "guid": "bfdfe7dc352907fc980b868725387e986e2976ba8451c24dd02a6c6d9dc052a2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9801b666523f252ae423d96981ffdcea4e", "guid": "bfdfe7dc352907fc980b868725387e98941544c221263f8727c5651833ac7bac"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9862a1bdb65136ba12be12ea5ec1bad567", "guid": "bfdfe7dc352907fc980b868725387e982a253298e0f3733c1b9507f412559828"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cbe67cf99f4c6cd3fbdf28bba6e7b757", "guid": "bfdfe7dc352907fc980b868725387e982021aca3f3a1b206818945093d5b26e3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985158060b055eda26b009ae1a099357ab", "guid": "bfdfe7dc352907fc980b868725387e98d9f834483786fc0c92f7593bcd6dd83c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a25b8cda66ec96b937ea4734bf681b71", "guid": "bfdfe7dc352907fc980b868725387e9824d27b3f537ef9a2045dede749d7c0d4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984820fb2677b0285ba5c83283a572d833", "guid": "bfdfe7dc352907fc980b868725387e980bed89934f29d483fb9437c1ee2c0997"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987d164dd58e9c95aa23032ffc9cb87b17", "guid": "bfdfe7dc352907fc980b868725387e9837785c89cc68442a317aa11bbdf8c941"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98edec51fd66091a41df6b5a79f509a3b1", "guid": "bfdfe7dc352907fc980b868725387e98ae9cfda66bc1e1bfc9abf45be96f0279"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988e13e15d294a8543241712ec800d63d4", "guid": "bfdfe7dc352907fc980b868725387e98f58eb8921a9742656ee627060f43a262"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a9cbc6ce7f694ebea2797d4ad28e4cdb", "guid": "bfdfe7dc352907fc980b868725387e98d3465a74f12a4301aa2d8c0d61f19e31"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98364c2f3eada5c93bf8f4cca8fbfc2916", "guid": "bfdfe7dc352907fc980b868725387e9871ed1b438edafbb39d8629bebe580cde"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987128eedac4a8ffed413cc6ed37b6014e", "guid": "bfdfe7dc352907fc980b868725387e9800ddb06d077111995f419ba9bca7f614"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9836b0f3bfc2eda5ea9eb1d272d53c0b06", "guid": "bfdfe7dc352907fc980b868725387e98001cf8cf1b1a8df5390c7082c81acf8d"}], "guid": "bfdfe7dc352907fc980b868725387e989d91e5abbad637a65716b7fef9466ccd", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9815a52416b22b7a4a55aab82cf72a39f4", "guid": "bfdfe7dc352907fc980b868725387e9847d3b3fdbf8c6c03a84819a79f7ae5aa"}], "guid": "bfdfe7dc352907fc980b868725387e988ec00323e57392a3c283b014e048e33b", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98c4b351c20ac090098b0dcaf5fd3c18e2", "targetReference": "bfdfe7dc352907fc980b868725387e98e1aba8ff8dc833f2269ce0a7182533b3"}], "guid": "bfdfe7dc352907fc980b868725387e98053f546dc39b1661db608be4eb007f0a", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e98e1aba8ff8dc833f2269ce0a7182533b3", "name": "geolocator_apple-geolocator_apple_privacy"}], "guid": "bfdfe7dc352907fc980b868725387e9821d372cc1e7c7587a12aeda843619e39", "name": "geolocator_apple", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e986ff8f87e011522b1b6328c84d9533927", "name": "geolocator_apple.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}