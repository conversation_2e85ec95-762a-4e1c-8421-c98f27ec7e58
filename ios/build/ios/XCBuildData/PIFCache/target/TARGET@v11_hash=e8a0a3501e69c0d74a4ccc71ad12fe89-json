{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98d65889532952df0709294a6081addd54", "buildSettings": {"CODE_SIGNING_ALLOWED": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/DKPhotoGallery/DKPhotoGallery-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/DKPhotoGallery/DKPhotoGallery-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/DKPhotoGallery/DKPhotoGallery.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "DKPhotoGallery", "PRODUCT_NAME": "DKPhotoGallery", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e981c32708ceaf22d7623fe654ea7886cc3", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98b528d7b725a8352ff1e55633ebb2695a", "buildSettings": {"CODE_SIGNING_ALLOWED": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/DKPhotoGallery/DKPhotoGallery-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/DKPhotoGallery/DKPhotoGallery-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/DKPhotoGallery/DKPhotoGallery.modulemap", "PRODUCT_MODULE_NAME": "DKPhotoGallery", "PRODUCT_NAME": "DKPhotoGallery", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98cb642bb1415e5a38b4333c1253a0bf5b", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98b528d7b725a8352ff1e55633ebb2695a", "buildSettings": {"CODE_SIGNING_ALLOWED": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/DKPhotoGallery/DKPhotoGallery-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/DKPhotoGallery/DKPhotoGallery-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/DKPhotoGallery/DKPhotoGallery.modulemap", "PRODUCT_MODULE_NAME": "DKPhotoGallery", "PRODUCT_NAME": "DKPhotoGallery", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e989b8f720e75a646a28424de94143397c2", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e987b7d0970ff91cc968227b81d1710693e", "guid": "bfdfe7dc352907fc980b868725387e98eaf3e9d67a63e64e4d35eb03af9ad7d6", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e986fcadaafd9ef398e22728126a25768e7", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98d2d0685bb3ef2ce6af6c278990a1559f", "guid": "bfdfe7dc352907fc980b868725387e989ec10ef72a4f7b1ecf45ba0cd3d3c6f7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983079be2e65f03ea0510d47a3008df1fc", "guid": "bfdfe7dc352907fc980b868725387e986b4cd50683c08b4e48dd526fc351ecb4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f5bc1a07776ac5a0c975d17bd5790a30", "guid": "bfdfe7dc352907fc980b868725387e981f160ef12a7d6ccafbcb5ec3e21588ed"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9860ca615c9d06d31ca6f18d667bfa816e", "guid": "bfdfe7dc352907fc980b868725387e98438c6aded8246a964f74349b99eea1d6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9853c3069be79213e7ab1234a47088dbb4", "guid": "bfdfe7dc352907fc980b868725387e98a5520b757a8ff133af59a3a2ea66b23d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984f9dd5f14089b2df6b08b73a25cc9921", "guid": "bfdfe7dc352907fc980b868725387e98f361ff46562bef8013d8d88f6492e229"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986a1ecff269206b9b97aef9060baf2b47", "guid": "bfdfe7dc352907fc980b868725387e98a49b38598ea22fe19e7fe3a37e9df9f5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98be284f196a9d543d1dc3992bdf666309", "guid": "bfdfe7dc352907fc980b868725387e98346ad77c615651ad1de7e6e0070bfac7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fc70d9a1013e769029a5bf380f89a705", "guid": "bfdfe7dc352907fc980b868725387e98011fec87f5fed0416c7953d99ab8e644"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9874f7fed31b4908ccbb41233c40d580c3", "guid": "bfdfe7dc352907fc980b868725387e9888b0fb1d1ed54781fe04b8812f15d228"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98277feb8584c7b5f866085f8a2c3084c4", "guid": "bfdfe7dc352907fc980b868725387e98612fe2cee93a66dceb710b99148b6d8e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988e0ef67bbd62c3e5887f07a1a49b774d", "guid": "bfdfe7dc352907fc980b868725387e980c48e90ef485c903f292ee07ce0dd045"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ff85b1d63548aa26563552ba7f7db23b", "guid": "bfdfe7dc352907fc980b868725387e9862a41e284f1e699f9f74b378f116a21d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9859d4388cf4a8e06602c3105a5cb31917", "guid": "bfdfe7dc352907fc980b868725387e9849542b3898cf4ddf891f93e554d8fdbf"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cb28e6ceb0452532874970f96d03401f", "guid": "bfdfe7dc352907fc980b868725387e981e9fa67f8ccdf254835038748a27bc50"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c1094badab1211ab437e7ed26cfc87fe", "guid": "bfdfe7dc352907fc980b868725387e9890c14a450ee46134a0217b59c942dd6c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982b098a4c67c265bdef02a899e7d3b4d0", "guid": "bfdfe7dc352907fc980b868725387e982803a9e6cbb61043039240116f31e8ea"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9840c423bb81d3689af9ac6581599d65b7", "guid": "bfdfe7dc352907fc980b868725387e9849afc839de092908e43a592374b94f2a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98027b0aaddf44a50720225282947e1079", "guid": "bfdfe7dc352907fc980b868725387e98d27ca8d15e17be5b95db37bc17fcd830"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980af1f034560da099614865475e850838", "guid": "bfdfe7dc352907fc980b868725387e98d33b237e4b9c5198f3fa9936f6d37245"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986c32773e5e9ff4e671de8784f6a18eaf", "guid": "bfdfe7dc352907fc980b868725387e98974b3a9f73616465e0f2d44d3d9fd96f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985a71b4d324d63bcdd7ccfc1b1bec556b", "guid": "bfdfe7dc352907fc980b868725387e984fd33c8c4f6c19d58993db7cb25ead3b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987f44d7d4ce6392eb8729535806e2680f", "guid": "bfdfe7dc352907fc980b868725387e988c2ecdf2e30f68b62a3b56c30f2b34ec"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9819018480e9f28f44c146dae53b3e607d", "guid": "bfdfe7dc352907fc980b868725387e982d2ed60da896d579bc9d4156783d9e49"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988e8c3316d7d9abf47ee4270f94d7aede", "guid": "bfdfe7dc352907fc980b868725387e984d0e6f922624e37ac4e06a42aedcfe30"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985afe7529195eae2e9c976ab74359eb4f", "guid": "bfdfe7dc352907fc980b868725387e982d6a185b47a2e48685f42f57a6231378"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9806343f3a8f75530379088c7f42daaee6", "guid": "bfdfe7dc352907fc980b868725387e9829f89d65c556bee2da2561e74eb845c7"}], "guid": "bfdfe7dc352907fc980b868725387e98a80fd2130c15c88ce8eb3c751d311d02", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e983480ab0a2790ef5114d32cf3547249b9", "guid": "bfdfe7dc352907fc980b868725387e98d4b969b3bc61befd54a4a74db1689eef"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b128dddad9e48f0b734ee6bf5b78dec3", "guid": "bfdfe7dc352907fc980b868725387e9886f4d86bd215a333fb3260d056d8c060"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9815a52416b22b7a4a55aab82cf72a39f4", "guid": "bfdfe7dc352907fc980b868725387e983772427d1cbcea19abdc16efa2cda65d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986a4dd600f511ecfb97b2973cee56fc7b", "guid": "bfdfe7dc352907fc980b868725387e98ad880efaa58abbf8efe17a888e3d041a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9863219d4e9a53430111b16a16d60de60c", "guid": "bfdfe7dc352907fc980b868725387e981093358bd6cd2c823951c9d0aa2f9eec"}], "guid": "bfdfe7dc352907fc980b868725387e98015b8c578f5020a4bc311f35c4da9744", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e9886511e7ac99230c86865639d14dfb340", "targetReference": "bfdfe7dc352907fc980b868725387e98d3f65728b12dd217475d1283ee417937"}], "guid": "bfdfe7dc352907fc980b868725387e98b4632cf793590f4614fe740dab7b4440", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98d3f65728b12dd217475d1283ee417937", "name": "DKPhotoGallery-DKPhotoGallery"}, {"guid": "bfdfe7dc352907fc980b868725387e98c46180aea4e87057640961e6db37df0d", "name": "SDWebImage"}, {"guid": "bfdfe7dc352907fc980b868725387e9872eabefc63c14dfe52fb0c95ad90294e", "name": "SwiftyGif"}], "guid": "bfdfe7dc352907fc980b868725387e989d0a1858a86fd6e6731ed20f88a1e515", "name": "DKPhotoGallery", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Swift", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e986e90c628ccd44af657bee5ff4af2f692", "name": "DKPhotoGallery.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}