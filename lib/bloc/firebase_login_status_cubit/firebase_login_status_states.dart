import 'package:bus/data/models/auth_models/firebase_login_status_models/firebase_login_status_models.dart';
import 'package:bus/helper/response_state.dart';
import 'package:equatable/equatable.dart';

/// States for Firebase login status management
/// Follows the same pattern as other state classes in the app
class FirebaseLoginStatusState extends Equatable {
  /// Firebase login status model containing the response data
  final FirebaseLoginStatusModels? firebaseLoginStatusModels;

  /// Current response state (loading, success, failure, etc.)
  final ResponseState rStates;

  /// Error or success message
  final String? message;

  const FirebaseLoginStatusState({
    this.firebaseLoginStatusModels,
    this.rStates = ResponseState.init,
    this.message,
  });

  /// Creates a copy of the current state with updated fields
  FirebaseLoginStatusState copyWith({
    FirebaseLoginStatusModels? firebaseLoginStatusModels,
    ResponseState? rStates,
    String? message,
  }) {
    return FirebaseLoginStatusState(
      firebaseLoginStatusModels:
          firebaseLoginStatusModels ?? this.firebaseLoginStatusModels,
      rStates: rStates ?? this.rStates,
      message: message ?? this.message,
    );
  }

  /// Helper getter to check if Firebase login is enabled (show button)
  bool get isFirebaseLoginEnabled =>
      firebaseLoginStatusModels?.isFirebaseLoginEnabled ?? false;

  /// Helper getter to check if Firebase login is disabled (hide button)
  bool get isFirebaseLoginDisabled =>
      firebaseLoginStatusModels?.isFirebaseLoginDisabled ?? false;

  /// Helper getter to check if we have valid login status data
  bool get hasLoginStatusData => firebaseLoginStatusModels != null;

  /// Helper getter to check if the request is currently loading
  bool get isLoading => rStates == ResponseState.loading;

  /// Helper getter to check if the request was successful
  bool get isSuccess => rStates == ResponseState.success;

  /// Helper getter to check if the request failed
  bool get isFailure => rStates == ResponseState.failure;

  /// Helper getter to check if the state is in initial state
  bool get isInitial => rStates == ResponseState.init;

  @override
  List<Object?> get props => [
        firebaseLoginStatusModels,
        rStates,
        message,
      ];

  @override
  String toString() {
    return 'FirebaseLoginStatusState(firebaseLoginStatusModels: $firebaseLoginStatusModels, rStates: $rStates, message: $message)';
  }
}
