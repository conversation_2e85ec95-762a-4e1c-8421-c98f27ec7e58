import 'package:shared_preferences/shared_preferences.dart';

class CacheHelper {
  static final CacheHelper _cacheHelper = CacheHelper._();
  factory CacheHelper() {
    return _cacheHelper;
  }
  CacheHelper._();

  static SharedPreferences? _sp;

  static Future<void> init() async {
    _sp = await SharedPreferences.getInstance();
  }

  static Future<bool> putString(String key, String value) async {
    if (_sp == null) await init();
    return await _sp!.setString(key, value);
  }

  static Future<bool> putInt(String key, int value) async {
    if (_sp == null) await init();
    return await _sp!.setInt(key, value);
  }

  static Future<bool> putBool(String key, dynamic value) async {
    if (_sp == null) await init();
    return await _sp!.setBool(key, value);
  }

  static Future<bool> putListOfString(String key, List<String> value) async {
    if (_sp == null) await init();
    return await _sp!.setStringList(key, value);
  }

  // static Future<bool> pusModals(String key, ProfileModals value) async {
  //   return await _sp!.setString(key, jsonEncode(ProfileModals));
  // }

  static List<String>? getListOfString(String key) {
    if (_sp == null) {
      // Return null if SharedPreferences is not initialized yet
      return null;
    }
    return _sp!.getStringList(key);
  }

  static bool? getBool(String key) {
    if (_sp == null) {
      // Return null if SharedPreferences is not initialized yet
      return null;
    }
    return _sp!.getBool(key);
  }

  static int? getInt(String key) {
    if (_sp == null) {
      // Return null if SharedPreferences is not initialized yet
      return null;
    }
    return _sp!.getInt(key);
  }

  static String? getString(String key) {
    if (_sp == null) {
      // Return null if SharedPreferences is not initialized yet
      return null;
    }
    return _sp!.getString(key);
  }

  static Future<bool> remove(String key) async {
    if (_sp == null) await init();
    return await _sp!.remove(key);
  }

  static Future<bool> clearAllSaved() async {
    if (_sp == null) await init();
    return await _sp!.clear();
  }
}
