import 'package:bus/bloc/absences_cubit/absences_cubit.dart';
import 'package:bus/bloc/add_bus_cubit/add_bus_cubit.dart';
import 'package:bus/bloc/add_driver_cubit/add_driver_cubit.dart';
import 'package:bus/bloc/add_remove_student_to_bus_cubit/add_remove_student_to_bus_cubit.dart';
import 'package:bus/bloc/add_student_file_cubit/add_student_file_cubit.dart';
import 'package:bus/bloc/add_suporvisor_cubit/add_supervisor_cubit.dart';
import 'package:bus/bloc/bus_available_cubit/bus_available_cubit.dart';
import 'package:bus/bloc/bus_location_tracker_cubit/bus_location_tracker_cubit.dart';
import 'package:bus/bloc/bus_supervisor_available_cubit/bus_supervisor_available_cubit.dart';
import 'package:bus/bloc/buses_cubit/buses_cubit.dart';
import 'package:bus/bloc/change_password_cubit/change_password_cubit.dart';
import 'package:bus/bloc/check_internet_cubit/check_internet_cubit.dart';
import 'package:bus/bloc/classroom__cubit/class_room_cubit.dart';
import 'package:bus/bloc/complete_profile_cubit/complete_profile_cubit.dart';
import 'package:bus/bloc/create_password_cubit/create_password_cubit.dart';
import 'package:bus/bloc/current_trip_cubit/current_trip_cubit.dart';
import 'package:bus/bloc/delete_bus_cubit/delete_bus_cubit.dart';
import 'package:bus/bloc/delete_driver_supervisor_cubit/delete_driver_supervisor_cubit.dart';
import 'package:bus/bloc/delete_student_cubit/delete_student_cubit.dart';
import 'package:bus/bloc/driver_cubit/driver_cubit.dart';
import 'package:bus/bloc/file_name_download_cubit/file_name_download_cubit.dart';
import 'package:bus/bloc/forgot_password_cubit/forgot_password_cubit.dart';
import 'package:bus/bloc/gender_cubit/gender_cubit.dart';
import 'package:bus/bloc/grade_cubit/grade_cubit.dart';
import 'package:bus/bloc/layout_cubit/layout_cubit.dart';
import 'package:bus/bloc/login_cubit/login_cubit.dart';
import 'package:bus/bloc/firebase_login_status_cubit/firebase_login_status_cubit.dart';
import 'package:bus/bloc/logout_cubit/logout_cubit.dart';
import 'package:bus/bloc/parent_cubit/parent_cubit.dart';
import 'package:bus/bloc/parent_cubit/parent_filter_cubit.dart';
import 'package:bus/bloc/profile_cubit/profile_cubit.dart';
import 'package:bus/bloc/profile_home_cubit/profile_home_cubit.dart';
import 'package:bus/bloc/register_cubit/register_cubit.dart';
import 'package:bus/bloc/religion_cubit/religion_cubit.dart';
import 'package:bus/bloc/send_code_verification_cubit/send_code_verification_cubit.dart';
import 'package:bus/bloc/student_bus_cubit/student_bus_cubit.dart';
import 'package:bus/bloc/student_cubit/student_cubit.dart';
import 'package:bus/bloc/subscription_cubit/subscription_cubit.dart';
import 'package:bus/bloc/supervisor_cubit/supervisor_cubit.dart';
import 'package:bus/bloc/type_blood_cubit/type_blood_cubit.dart';
import 'package:bus/bloc/update_bus_cubit/update_bus_cubit.dart';
import 'package:bus/bloc/update_driver_cubit/update_driver_cubit.dart';
import 'package:bus/bloc/update_profile_cubit/update_profile_cubit.dart';
import 'package:bus/bloc/update_suporvisor_cubit/update_supervisor_states.dart';
import 'package:bus/config/global_variable.dart';
import 'package:bus/config/route.dart';
import 'package:bus/data/repo/create_password_repo.dart';
import 'package:bus/firebase_options.dart';
import 'package:bus/helper/cache_helper.dart';
import 'package:bus/services/notification_service/local_notification_service.dart';
import 'package:bus/services/notification_service/utils/logger.dart';
import 'package:bus/services/payment_service.dart';
import 'package:bus/translations/local_keys.g.dart';
import 'package:bus/utils/get_it_injection.dart';
import 'package:bus/utils/navigation_helper.dart';
import 'package:bus/utils/app_startup_helper.dart';
import 'package:bus/services/app_update_service.dart';
import 'package:bus/views/screens/languages_screen/on_boarding_language_screen.dart';
import 'package:bus/views/screens/layout_screen/layout_screen.dart';
import 'package:bus/widgets/carousel_widget/carousel_cubit/carousel_cubit.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_gcaptcha_v3/recaptca_config.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'bloc/ads_cubit/ads_cubit.dart';
import 'bloc/notifications_cubit/notifications_cubit.dart';
import 'bloc/previous_trips_cubit/previous_trips_cubit.dart';
import 'bloc/attendance_cubit/attendance_cubit.dart';
import 'bloc/route_cubit/route_cubit.dart';
import 'bloc/student_cubit/student_filter_cubit.dart';
import 'package:timezone/data/latest.dart' as tz;

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  try {
    // Initialize Firebase with proper options
    await Firebase.initializeApp(
      options: DefaultFirebaseOptions.currentPlatform,
    );
    Logger.i('Firebase Core initialized');

    // Initialize CacheHelper first
    await CacheHelper.init();
    Logger.i('CacheHelper initialized');

    // Initialize timezone
    tz.initializeTimeZones();
    Logger.i('Timezone initialized');

    // Initialize GetIt dependencies
    await init();
    Logger.i('GetIt dependencies initialized');

    await EasyLocalization.ensureInitialized();
    await PaymentService.instance.initConnection();
  } catch (e) {
    debugPrint('Initialization failed: $e');
  }

  RecaptchaHandler.instance
      .setupSiteKey(dataSiteKey: '6LfBLQgqAAAAAHbIydaPI39vVgIcRzsvP0EazIxk');

  // Get token and Google sign-in flag after CacheHelper is initialized
  token = CacheHelper.getString("token");
  isGoogleSignIn = CacheHelper.getBool("isGoogleSignIn") ?? false;
  debugPrint("Main - isGoogleSignIn: $isGoogleSignIn");

  SystemChrome.setPreferredOrientations([
    DeviceOrientation.portraitUp,
    DeviceOrientation.portraitDown,
  ]);
  runApp(
    EasyLocalization(
      supportedLocales: const [Locale('en'), Locale('ar')],
      path: 'assets/translations',
      fallbackLocale: const Locale('en'),
      child: MyApp(
        token: token,
      ),
    ),
  );

  // Initialize notification services after the app is running
  await Future.delayed(const Duration(seconds: 1));

  // Use GetIt to get the singleton instance of LocalNotificationService
  // This ensures we don't initialize it multiple times
  final notificationService = getIt<LocalNotificationService>();

  // Initialize only if not already initialized
  if (!LocalNotificationService.isInitialized) {
    await notificationService.initialize();
    Logger.i('Notification service initialized from main');
  } else {
    Logger.i('Notification service already initialized, skipping');
  }

  // Get FCM token last
  final fcmToken = await FirebaseMessaging.instance.getToken();
  Logger.firebase('FCM Token: $fcmToken');
  initializeFCMToken();
}

class MyApp extends StatelessWidget {
  final String? token;
  const MyApp({
    super.key,
    this.token,
  });
  @override
  Widget build(BuildContext context) {
    return MultiBlocProvider(
      providers: [
        BlocProvider(
          create: (context) => LayoutCubit(),
        ),
        BlocProvider(
          create: (context) => RegisterCubit(),
        ),
        BlocProvider(
          create: (context) => LoginCubit(),
        ),
        BlocProvider(
          create: (context) => FirebaseLoginStatusCubit(),
        ),
        BlocProvider(
          create: (context) => ProfileCubit()..getProfile(),
        ),
        BlocProvider(
          create: (context) => ChangePasswordCubit(),
        ),
        BlocProvider(
          create: (context) => CreatePasswordCubit(getIt<CreatePasswordRepo>()),
        ),
        BlocProvider(
          create: (context) => UpdateProfileCubit(),
        ),
        BlocProvider(
          create: (context) => LogoutCubit(),
        ),
        BlocProvider(
          create: (context) => StudentCubit(),
        ),
        BlocProvider(
          create: (context) => StudentBusCubit(),
        ),
        BlocProvider(
          create: (context) => ParentCubit(),
        ),
        BlocProvider(
          create: (context) => DriverCubit(),
        ),
        BlocProvider(
          create: (context) => BusLocationTrackerCubit(),
        ),
        BlocProvider(
          create: (context) => BusesCubit(),
        ),
        BlocProvider(
          create: (context) => AbsencesCubit(),
        ),
        BlocProvider(
          create: (context) => SupervisorCubit(),
        ),
        BlocProvider(
          create: (context) => ClassRoomCubit(),
        ),
        BlocProvider(
          create: (context) => DeleteStudentCubit(),
        ),
        BlocProvider(
          create: (context) => DeleteBusCubit(),
        ),
        BlocProvider(
          create: (context) => GradeCubit()..getGrade(),
        ),
        BlocProvider(
          create: (context) => GenderCubit(),
        ),
        BlocProvider(
          create: (context) => ReligionCubit()..getReligion(),
        ),
        BlocProvider(
          create: (context) => TypeBloodCubit()..getTypeBlood(),
        ),
        BlocProvider(
          create: (context) => NotificationsCubit(),
        ),
        BlocProvider(
          create: (context) => ForgotPasswordCubit(),
        ),
        BlocProvider(
          create: (context) => CarouselCubit(),
        ),
        BlocProvider(
          create: (context) => AdsCubit(),
        ),
        BlocProvider(
          create: (context) => BusAvailableCubit(),
        ),
        BlocProvider(
          create: (context) => AddStudentFileCubit(),
        ),
        BlocProvider(
          create: (context) => BusSupervisorAvailableCubit(),
        ),
        BlocProvider(
          create: (context) => DeleteDriverSupervisorCubit(),
        ),
        BlocProvider(
          create: (context) => AddBusCubit(),
        ),
        BlocProvider(
          create: (context) => UpdateBusCubit(),
        ),
        BlocProvider(
          create: (context) => AddDriverCubit(),
        ),
        BlocProvider(
          create: (context) => UpdateDriverCubit(),
        ),
        BlocProvider(
          create: (context) => AddSupervisorCubit(),
        ),
        BlocProvider(
          create: (context) => UpdateSupervisorCubit(),
        ),
        BlocProvider(
          create: (context) => CheckInternetCubit(),
        ),
        BlocProvider(
          create: (context) => ProfileHomeCubit(),
        ),
        BlocProvider(
          create: (context) => CurrentTripCubit()..getCurrentTrip(),
        ),
        BlocProvider(
          create: (context) => AddRemoveStudentToBusCubit(),
        ),
        BlocProvider(
          create: (context) => ParentFilterCubit(),
        ),
        BlocProvider(
          create: (context) => SendCodeVerificationCubit(),
        ),
        BlocProvider(
          create: (context) => StudentFilterCubit(),
        ),
        BlocProvider(
          create: (context) => FileNameDownloadCubit(),
        ),
        BlocProvider(
          create: (context) => SubscriptionCubit(),
        ),
        BlocProvider(
          create: (context) => CompleteProfileCubit(),
        ),
        BlocProvider(
          create: (context) => PreviousTripsCubit(),
        ),
        BlocProvider(
          create: (context) => AttendanceCubit(),
        ),
        BlocProvider(
          create: (context) => RouteCubit(),
        ),
      ],
      child: ScreenUtilInit(
        designSize: const Size(428, 926),
        builder: (context, states) {
          return MaterialApp(
            title: AppStrings.busatySchoolTitle.tr(),
            debugShowCheckedModeBanner: false,
            scaffoldMessengerKey: snackBarKey,
            navigatorKey: getIt<NavHelper>().navigatorKey,
            localizationsDelegates: context.localizationDelegates,
            supportedLocales: context.supportedLocales,
            locale: CacheHelper.getString('lang') == 'ar'
                ? const Locale('ar')
                : const Locale('en'),
            theme: ThemeData(
              primarySwatch: Colors.blue,
            ),
            routes: routes,
            home: AppStatusWrapper(
              appName: 'busaty-schools',
              appType: AppType.schools,
              child: token != null
                  ? const LayoutScreen()
                  : const OnBoardingLanguageScreen(),
            ),
          );
        },
      ),
    );
  }
}
