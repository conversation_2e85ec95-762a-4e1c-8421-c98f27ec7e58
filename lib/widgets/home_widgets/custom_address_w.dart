import 'package:bus/bloc/profile_cubit/profile_cubit.dart';
import 'package:bus/bloc/profile_cubit/profile_states.dart';
import 'package:bus/config/theme_colors.dart';
import 'package:bus/helper/response_state.dart';
import 'package:bus/views/screens/profile_screen/profile_screen.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class CustomAddressW extends StatelessWidget {
  const CustomAddressW({super.key});

  @override
  Widget build(BuildContext context) {
    final isRTL = context.locale.languageCode == 'ar';

    return BlocBuilder<ProfileCubit, ProfileStates>(
      builder: (context, states) {
        if (states.rStates == ResponseState.loading) {
          return _buildShimmerLoading();
        } else if (states.rStates == ResponseState.success) {
          return Container(
            width: 354.w,
            height: 100.w,
            margin: EdgeInsets.symmetric(horizontal: 16.w),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                // Adjust gradient direction based on language
                begin: isRTL ? Alignment.topRight : Alignment.topLeft,
                end: isRTL ? Alignment.bottomLeft : Alignment.bottomRight,
                colors: [
                  TColor.mainColor.withOpacity(0.65),
                  TColor.mainColor,
                  TColor.mainColor.withOpacity(0.65),
                ],
              ),
              borderRadius: BorderRadius.circular(20.r),
              boxShadow: [
                BoxShadow(
                  color: TColor.mainColor.withOpacity(0.3),
                  blurRadius: 12,
                  offset: const Offset(0, 6),
                ),
              ],
            ),
            child: Material(
              color: Colors.transparent,
              child: InkWell(
                borderRadius: BorderRadius.circular(20.r),
                onTap: () {
                  Navigator.pushNamed(context, ProfileScreen.routeName);
                },
                child: Stack(
                  children: [
                    // Adjust background circle position based on language
                    Positioned(
                      right: isRTL ? null : -20,
                      left: isRTL ? -20 : null,
                      top: -20,
                      child: Container(
                        width: 100.w,
                        height: 100.w,
                        decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          color: Colors.white.withOpacity(0.1),
                        ),
                      ),
                    ),
                    Padding(
                      padding: EdgeInsets.symmetric(horizontal: 20.w),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          _buildAvatar(states),
                          SizedBox(width: 16.w),
                          Expanded(
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.center,
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                _buildName(states),
                                SizedBox(height: 6.h),
                                _buildAddress(states, isRTL),
                              ],
                            ),
                          ),
                          Container(
                            padding: EdgeInsets.all(8.w),
                            decoration: BoxDecoration(
                              color: Colors.white.withOpacity(0.2),
                              shape: BoxShape.circle,
                            ),
                            child: Icon(
                              isRTL
                                  ? Icons.arrow_forward_ios
                                  : Icons.arrow_back_ios,
                              size: 16.sp,
                              color: TColor.white,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),
          );
        } else {
          return _buildErrorState();
        }
      },
    );
  }

  Widget _buildShimmerLoading() {
    return Container(
      width: 354.w,
      height: 90.w,
      margin: EdgeInsets.symmetric(horizontal: 16.w),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16.r),
      ),
      child: Row(
        children: [
          Container(
            margin: EdgeInsets.all(16.w),
            width: 60.w,
            height: 60.w,
            decoration: BoxDecoration(
              color: Colors.grey[200],
              shape: BoxShape.circle,
            ),
          ),
          Expanded(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Container(
                  width: 120.w,
                  height: 16.h,
                  decoration: BoxDecoration(
                    color: Colors.grey[200],
                    borderRadius: BorderRadius.circular(8.r),
                  ),
                ),
                SizedBox(height: 8.h),
                Container(
                  width: 200.w,
                  height: 12.h,
                  decoration: BoxDecoration(
                    color: Colors.grey[200],
                    borderRadius: BorderRadius.circular(6.r),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAvatar(ProfileStates states) {
    return Container(
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        border: Border.all(color: Colors.white.withOpacity(0.2), width: 2),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: CircleAvatar(
        backgroundColor: TColor.white.withOpacity(0.9),
        radius: 28.r, // Slightly smaller
        child:
            states.userModel?.logoPath != null
                ? ClipRRect(
                  borderRadius: BorderRadius.circular(28.r),
                  child: Image.network(
                    states.userModel!.logoPath!,
                    fit: BoxFit.cover,
                    errorBuilder: (context, error, stackTrace) {
                      return Icon(
                        Icons.person,
                        size: 28.r,
                        color: TColor.mainColor,
                      );
                    },
                  ),
                )
                : Icon(Icons.person, size: 28.r, color: TColor.mainColor),
      ),
    );
  }

  Widget _buildName(ProfileStates states) {
    return Text(
      states.userModel?.name ?? '',
      style: TextStyle(
        color: TColor.white,
        fontSize: 18.sp, // Slightly larger
        fontWeight: FontWeight.w600,
        letterSpacing: 0.5,
      ),
      maxLines: 1,
      overflow: TextOverflow.ellipsis,
    );
  }

  Widget _buildAddress(ProfileStates states, bool isRTL) {
    return Row(
      children: [
        Icon(
          Icons.location_on,
          size: 14.sp,
          color: TColor.white.withOpacity(0.7),
        ),
        SizedBox(width: 4.w),
        Expanded(
          child: Text(
            states.userModel?.address ?? '',
            style: TextStyle(
              color: TColor.white.withOpacity(0.7),
              fontSize: 14.sp,
              fontWeight: FontWeight.w400,
            ),
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
        ),
      ],
    );
  }

  Widget _buildErrorState() {
    return Container(
      width: 354.w,
      height: 100.w,
      margin: EdgeInsets.symmetric(horizontal: 16.w),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20.r),
        border: Border.all(color: Colors.red.withOpacity(0.3)),
        boxShadow: [
          BoxShadow(
            color: Colors.red.withOpacity(0.1),
            blurRadius: 8,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Center(
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.error_outline, color: Colors.red, size: 20.sp),
            SizedBox(width: 8.w),
            Text(
              'Failed to load profile',
              style: TextStyle(color: Colors.red, fontSize: 14.sp),
            ),
          ],
        ),
      ),
    );
  }
}
