import 'package:bus/config/global_variable.dart';
import 'package:bus/config/theme_colors.dart';
import 'package:bus/utils/sized_box.dart';
import 'package:bus/views/custom_widgets/custom_text.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:bus/translations/local_keys.g.dart';
import '../../bloc/notifications_cubit/notifications_cubit.dart';

import '../../views/screens/notifications_screen/notifications_screen.dart';

class CustomNameW extends StatelessWidget {
  final String? name;
  const CustomNameW({super.key, this.name = ""});

  @override
  Widget build(BuildContext context) {
    int hour = DateTime.now().hour;
    return Padding(
      padding: EdgeInsets.only(top: 10.w, right: 20.w, left: 20.w),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              CustomText(
                text:
                    hour >= 5 && hour < 12
                        ? AppStrings.goodMorning.tr()
                        : AppStrings.goodEvening.tr(),
                color: TColor.white,
                fontW: FontWeight.w600,
                fontSize: 18,
              ),
              const SBox(h: 3),
              CustomText(
                // Use userName from global variables if available, otherwise use the name passed as parameter
                text:
                    userName != null && userName!.isNotEmpty ? userName! : name,
                color: TColor.white,
                fontW: FontWeight.w400,
                fontSize: 14,
              ),
            ],
          ),
          InkWell(
            onTap: () {
              Navigator.pushNamed(context, NotificationsScreen.routeName);
              NotificationsCubit.get(
                context,
              ).getNotifications(page: 1, isFirst: true);
            },
            child: const Icon(Icons.notifications_none, color: TColor.white),
          ),
        ],
      ),
    );
  }
}
