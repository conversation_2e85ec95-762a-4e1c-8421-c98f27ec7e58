import 'package:bus/bloc/check_internet_cubit/check_internet_cubit.dart';
import 'package:bus/bloc/current_trip_cubit/current_trip_cubit.dart';
import 'package:bus/bloc/current_trip_cubit/current_trip_states.dart';
import 'package:bus/bloc/profile_cubit/profile_cubit.dart';
import 'package:bus/bloc/register_cubit/register_cubit.dart';
import 'package:bus/config/theme_colors.dart';
import 'package:bus/constant/path_route_name.dart';
import 'package:bus/data/repo/user_repo.dart';
import 'package:bus/helper/cache_helper.dart';
import 'package:bus/translations/local_keys.g.dart';
import 'package:bus/views/screens/login_screen/login_screen.dart';
import 'package:bus/widgets/custom_alert_dialog.dart';
import 'package:bus/widgets/home_widgets/custom_address_w.dart';
import 'package:bus/widgets/home_widgets/custom_gird_w.dart';
import 'package:bus/widgets/home_widgets/custom_name_w.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:provider/provider.dart';
import 'package:url_launcher/url_launcher.dart';

import '../../../bloc/ads_cubit/ads_cubit.dart';
import '../../../config/global_variable.dart';
import '../../../widgets/carousel_widget/carousel_widget.dart';
import '../../custom_widgets/custom_text.dart';

class HomeScreen extends StatefulWidget {
  static const String routeName = PathRouteName.home;

  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  final _checkHaveSchoolRepo = UserRepo();

  void checkAppVersion() {
    if (!mounted) return;

    // Get current version from Info.plist/build.gradle
    final currentVersion = "1.5.0+21"; // Current app version
    final requiredVersion = context.read<RegisterCubit>().version;

    debugPrint("requiredVersion $requiredVersion");
    debugPrint("currentVersion $currentVersion");

    if (requiredVersion != null) {
      final needsUpdate = requiredVersion != currentVersion;
      debugPrint("needsUpdate $needsUpdate");
      if (needsUpdate && mounted) {
        Future.microtask(() {
          if (mounted) {
            showForceUpdateDialog(
              context,
              Theme.of(context).platform == TargetPlatform.iOS
                  ? "https://apps.apple.com/eg/app/busaty-school/id6740827987?l=ar"
                  : "https://play.google.com/store/apps/details?id=com.busaty.school",
            );
          }
        });
      }
    }
  }

  Future<void> fetchUserStatus() async {
    if (!mounted) return;

    // Assuming you have a default name or you can pass the name as needed
    String defaultName = "schools";
    await context.read<RegisterCubit>().getUserStatus(defaultName);

    // Handle the status as needed
    if (mounted) {
      debugPrint("User Status: ${context.read<RegisterCubit>().getStatus}");
      setState(() {});
    }
  }

  @override
  void initState() {
    super.initState();

    // Load the Google sign-in flag from cache
    isGoogleSignIn = CacheHelper.getBool("isGoogleSignIn") ?? false;
    debugPrint("isGoogleSignIn: $isGoogleSignIn");

    fetchUserStatus().then((value) {
      if (!mounted) return;
      initializeFCMToken();
      AdsCubit.get(context).getAds();
      context.read<CheckInternetCubit>().checkConnectivity();

      // Refresh profile data to ensure latest user information is displayed
      context.read<ProfileCubit>().getProfile();

      _checkHaveSchoolRepo.repoHome().then((value) {
        if (mounted) {
          haveSchoolCheck(checkError: value.errors, context: context);
        }
      });

      checkAppVersion();
    });
  }

  haveSchoolCheck({bool? checkError, BuildContext? context}) {
    if (checkError == true &&
        Provider.of<CheckInternetCubit>(context!, listen: false).disConnected ==
            false) {
      CacheHelper.remove("token");
      token = null;
      Navigator.pushReplacementNamed(context, LoginScreen.routeName);
    }
  }

  @override
  Widget build(BuildContext context) {
    debugPrint('token: $token');
    debugPrint('tempToken: $tempToken');
    final nameArg = ModalRoute.of(context)!.settings.arguments;
    return StreamBuilder<Object>(
      stream: Connectivity().onConnectivityChanged,
      builder: (context, snapshot) {
        context.read<CheckInternetCubit>().checkConnectionStream(
          data: snapshot,
          context: context,
        );
        return RefreshIndicator(
          onRefresh: () async {
            if (!mounted) return Future.value();

            // Get data
            AdsCubit.get(context).getAds();
            context.read<CurrentTripCubit>().getCurrentTrip();

            // Refresh profile data to ensure latest user information is displayed
            await context.read<ProfileCubit>().getProfile();

            debugPrint('refresh');
            debugPrint('subscriptionStatus: $subscriptionStatus');

            // Check subscription status after refresh
            if (mounted &&
                subscriptionStatus != null &&
                subscriptionStatus == false) {
              debugPrint('subscriptionStatus: $subscriptionStatus');
              // Show subscription alert
              WidgetsBinding.instance.addPostFrameCallback((_) {
                if (mounted) {
                  showSubscriptionAlert(context);
                }
              });
            }

            return Future.value();
          },
          child: SingleChildScrollView(
            child: SafeArea(
              child:
                  context.watch<CheckInternetCubit>().disConnected
                      ? Stack(
                        children: [
                          Container(
                            width: 1.sw,
                            height: 1.sh,
                            decoration: const BoxDecoration(
                              image: DecorationImage(
                                image: AssetImage("assets/images/bg.png"),
                                fit: BoxFit.cover,
                              ),
                            ),
                            child:
                                nameArg != null
                                    ? CustomNameW(name: nameArg.toString())
                                    : CustomNameW(name: userName ?? ""),
                          ),
                          Positioned(
                            top: 65.w,
                            child: Container(
                              width: 1.sw,
                              height: 1.sh,
                              decoration: BoxDecoration(
                                color: TColor.white,
                                borderRadius: BorderRadius.only(
                                  topRight: Radius.circular(30.r),
                                  topLeft: Radius.circular(30.r),
                                ),
                              ),
                              child: Column(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  Image.asset(
                                    'assets/images/disconnect_internet.png',
                                  ),
                                  CustomText(
                                    text:
                                        AppStrings.checkInternetConnection.tr(),
                                    fontSize: 25,
                                    fontW: FontWeight.w600,
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ],
                      )
                      : Stack(
                        children: [
                          Container(
                            width: 1.sw,
                            height: 1.sh,
                            decoration: const BoxDecoration(
                              image: DecorationImage(
                                image: AssetImage("assets/images/bg.png"),
                                fit: BoxFit.cover,
                              ),
                            ),
                            child:
                                nameArg != null
                                    ? CustomNameW(name: nameArg.toString())
                                    : CustomNameW(name: userName ?? ""),
                          ),
                          Positioned(
                            top: 65.w,
                            child: Container(
                              width: 1.sw,
                              height: 1.sh,
                              decoration: BoxDecoration(
                                color: TColor.white,
                                borderRadius: BorderRadius.only(
                                  topRight: Radius.circular(30.r),
                                  topLeft: Radius.circular(30.r),
                                ),
                              ),
                              child: SingleChildScrollView(
                                child: Column(
                                  children: [
                                    20.verticalSpace,
                                    BlocBuilder<AdsCubit, AdsState>(
                                      builder: (context, state) {
                                        final ads =
                                            AdsCubit.get(context).ads?.data;
                                        return CarouselWidget(
                                          items: List<Widget>.generate(
                                            ads?.length ?? 0,
                                            (index) => InkWell(
                                              onTap: () async {
                                                final Uri url = Uri.parse(
                                                  ads?[index].ads?.link ?? '',
                                                );
                                                // Store context in a local variable before any async operations
                                                final scaffoldMessenger =
                                                    ScaffoldMessenger.of(
                                                      context,
                                                    );

                                                try {
                                                  if (!await launchUrl(url)) {
                                                    if (mounted) {
                                                      scaffoldMessenger
                                                          .showSnackBar(
                                                            const SnackBar(
                                                              content: Text(
                                                                'Could not launch URL',
                                                              ),
                                                            ),
                                                          );
                                                    }
                                                  }
                                                } catch (e) {
                                                  debugPrint(
                                                    'Error launching URL: $e',
                                                  );
                                                  if (mounted) {
                                                    scaffoldMessenger.showSnackBar(
                                                      const SnackBar(
                                                        content: Text(
                                                          'Error launching URL',
                                                        ),
                                                      ),
                                                    );
                                                  }
                                                }
                                              },
                                              child: Column(
                                                crossAxisAlignment:
                                                    CrossAxisAlignment.start,
                                                children: [
                                                  Container(
                                                    width: double.infinity,
                                                    height: 170.h,
                                                    decoration: BoxDecoration(
                                                      borderRadius:
                                                          const BorderRadius.only(
                                                            topLeft:
                                                                Radius.circular(
                                                                  10,
                                                                ),
                                                            topRight:
                                                                Radius.circular(
                                                                  10,
                                                                ),
                                                          ),
                                                      image: DecorationImage(
                                                        image: NetworkImage(
                                                          ads?[index]
                                                                  .ads
                                                                  ?.imagePath ??
                                                              '',
                                                        ),
                                                        fit: BoxFit.fill,
                                                      ),
                                                    ),
                                                  ),
                                                  Container(
                                                    width: double.infinity,
                                                    padding: EdgeInsets.only(
                                                      bottom: 6.h,
                                                      top: 6.h,
                                                    ),
                                                    decoration: const ShapeDecoration(
                                                      gradient: LinearGradient(
                                                        begin: Alignment(
                                                          0.00,
                                                          -1.00,
                                                        ),
                                                        end: Alignment(0, 1),
                                                        colors: [
                                                          TColor.mainColor,
                                                          TColor.borderAvatar,
                                                        ],
                                                      ),
                                                      shape: RoundedRectangleBorder(
                                                        borderRadius:
                                                            BorderRadius.only(
                                                              bottomLeft:
                                                                  Radius.circular(
                                                                    10,
                                                                  ),
                                                              bottomRight:
                                                                  Radius.circular(
                                                                    10,
                                                                  ),
                                                            ),
                                                      ),
                                                    ),
                                                    child: CustomText(
                                                      text:
                                                          ads?[index]
                                                              .ads
                                                              ?.title,
                                                      textAlign:
                                                          TextAlign.center,
                                                      fontSize: 18.sp,
                                                      fontW: FontWeight.w500,
                                                      color: Colors.white,
                                                    ),
                                                  ),
                                                ],
                                              ),
                                            ),
                                          ),
                                        );
                                      },
                                    ),
                                    20.verticalSpace,
                                    const CustomAddressW(),
                                    30.verticalSpace,
                                    const CustomGirdW(),
                                    30.verticalSpace,
                                    BlocBuilder<
                                      CurrentTripCubit,
                                      CurrentTripStates
                                    >(
                                      builder: (context, states) {
                                        if (states
                                            is CurrentTripLoadingStates) {
                                          return const SizedBox();
                                        } else if (states
                                            is CurrentTripSuccessStates) {
                                          if (states
                                                  .currentTripModels!
                                                  .data!
                                                  .isEmpty ==
                                              true) {
                                            return const SizedBox();
                                          } else {
                                            return InkWell(
                                              onTap: () {
                                                if (subscriptionStatus !=
                                                        null &&
                                                    subscriptionStatus ==
                                                        false) {
                                                  debugPrint(
                                                    'subscriptionStatus: $subscriptionStatus',
                                                  );
                                                  showSubscriptionAlert(
                                                    context,
                                                  );
                                                } else {
                                                  Navigator.pushNamed(
                                                    context,
                                                    PathRouteName
                                                        .openTripScreen,
                                                  );
                                                }
                                              },
                                              child: Container(
                                                width: 354.w,
                                                height: 50.w,
                                                decoration: BoxDecoration(
                                                  border: Border.all(
                                                    color:
                                                        TColor.borderContainer,
                                                  ),
                                                  borderRadius:
                                                      BorderRadius.circular(
                                                        14.r,
                                                      ),
                                                ),
                                                child: Center(
                                                  child: CustomText(
                                                    text:
                                                        AppStrings.openTrips
                                                            .tr(),
                                                    fontW: FontWeight.w700,
                                                    color:
                                                        TColor.borderContainer,
                                                    fontSize: 18,
                                                  ),
                                                ),
                                              ),
                                            );
                                          }
                                        } else if (states
                                            is CurrentTripErrorStates) {
                                          return const SizedBox();
                                        } else {
                                          return const SizedBox();
                                        }
                                      },
                                    ),
                                  ],
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
            ),
          ),
        );
      },
    );
  }
}
