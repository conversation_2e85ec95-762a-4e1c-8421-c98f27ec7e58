{"editor.tabCompletion": "on", "diffEditor.codeLens": true, "java.configuration.updateBuildConfiguration": "automatic", "cSpell.words": ["Busaty"], "dart.flutterSdkPath": null, "dart.projectSearchDepth": 5, "dart.enableSdkFormatter": true, "dart.lineLength": 80, "dart.insertArgumentPlaceholders": false, "dart.showTodos": true, "dart.runPubGetOnPubspecChanges": true, "dart.warnWhenEditingFilesOutsideWorkspace": false, "dart.analysisServerFolding": true, "dart.closingLabels": true, "dart.flutterCreateAndroidLanguage": "kotlin", "dart.flutterCreateIOSLanguage": "swift", "dart.flutterCreateOrganization": "com.busaty", "files.associations": {"*.dart": "dart"}, "editor.formatOnSave": true, "editor.codeActionsOnSave": {"source.fixAll": "explicit"}, "flutter.workspaceFolder": "./school_web_app"}