                        -H/Users/<USER>/Developer/flutter/packages/flutter_tools/gradle/src/main/groovy
-DCMAKE_SYSTEM_NAME=Android
-DCMAKE_EXPORT_COMPILE_COMMANDS=ON
-DCMAKE_SYSTEM_VERSION=23
-<PERSON><PERSON><PERSON>OID_PLATFORM=android-23
-D<PERSON>DROID_ABI=armeabi-v7a
-DCMAKE_ANDROID_ARCH_ABI=armeabi-v7a
-DANDROID_NDK=/Users/<USER>/Library/Android/sdk/ndk/25.1.8937393
-DCMAKE_ANDROID_NDK=/Users/<USER>/Library/Android/sdk/ndk/25.1.8937393
-DCMAKE_TOOLCHAIN_FILE=/Users/<USER>/Library/Android/sdk/ndk/25.1.8937393/build/cmake/android.toolchain.cmake
-DCMAKE_MAKE_PROGRAM=/Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/ninja
-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=/Users/<USER>/StudioProjects/test_notifictions/build/app/intermediates/cxx/RelWithDebInfo/1o2j3p5o/obj/armeabi-v7a
-DCMAKE_RUNTIME_OUTPUT_DIRECTORY=/Users/<USER>/StudioProjects/test_notifictions/build/app/intermediates/cxx/RelWithDebInfo/1o2j3p5o/obj/armeabi-v7a
-DCMAKE_BUILD_TYPE=RelWithDebInfo
-B/Users/<USER>/StudioProjects/test_notifictions/android/app/.cxx/RelWithDebInfo/1o2j3p5o/armeabi-v7a
-GNinja
-Wno-dev
--no-warn-unused-cli
                        Build command args: []
                        Version: 2