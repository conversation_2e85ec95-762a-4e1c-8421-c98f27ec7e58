package com.busaty.parent

import io.flutter.embedding.android.FlutterActivity
import android.os.Bundle
import android.os.Build
import androidx.core.view.WindowCompat
import androidx.core.view.WindowInsetsCompat
import androidx.core.view.WindowInsetsControllerCompat

class MainActivity: FlutterActivity() {
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        
        // Use WindowCompat instead of deprecated window methods
        WindowCompat.setDecorFitsSystemWindows(window, false)
        
        // Get the WindowInsetsController
        val windowInsetsController = WindowCompat.getInsetsController(window, window.decorView)
        
        // Configure system bars appearance
        windowInsetsController.apply {
            // Show system bars by default
            show(WindowInsetsCompat.Type.systemBars())
            
            // Make system bars appear semi-transparent
            isAppearanceLightStatusBars = true
            isAppearanceLightNavigationBars = true
        }
    }
}
