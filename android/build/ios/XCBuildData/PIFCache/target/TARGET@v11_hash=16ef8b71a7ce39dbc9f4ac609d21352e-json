{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98a8fb843795737f14de0cd187c5536f3e", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGNING_ALLOWED": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/nanopb/nanopb-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/nanopb/nanopb-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/nanopb/nanopb.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "nanopb", "PRODUCT_NAME": "nanopb", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98e481b12d595f218dc36407cb45769ca7", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98e46a1e0edb81831eef604b36cf8f99eb", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGNING_ALLOWED": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/nanopb/nanopb-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/nanopb/nanopb-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/nanopb/nanopb.modulemap", "PRODUCT_MODULE_NAME": "nanopb", "PRODUCT_NAME": "nanopb", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e984fe8860e761ac68428dbae43cf2c94bc", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98e46a1e0edb81831eef604b36cf8f99eb", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGNING_ALLOWED": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/nanopb/nanopb-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/nanopb/nanopb-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/nanopb/nanopb.modulemap", "PRODUCT_MODULE_NAME": "nanopb", "PRODUCT_NAME": "nanopb", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98473cc6c475a83f1fad5ae78dc73521f4", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98330d452f373c58d2bb224127c477f422", "guid": "bfdfe7dc352907fc980b868725387e981135938bf31c18964d9e5facb2ef321a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98754e5dc59ceff3f3ac190f07a7a791ef", "guid": "bfdfe7dc352907fc980b868725387e985b5a6d232c61e675dc3d8a3b5b84180e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984e64243738bc47582dcfd92ec8cfe9c2", "guid": "bfdfe7dc352907fc980b868725387e98f53134939ffbed63112591528ccc14f9", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d4c56fd9d2aafdc13ff291c80722caf7", "guid": "bfdfe7dc352907fc980b868725387e9833504116fdb02d5e0d9e223827d9a18b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ead602bb3aa01747752892f8a2fed19e", "guid": "bfdfe7dc352907fc980b868725387e98d4d11196fcb2320112fab23964e9e630", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e9822c7047e98a6f80158f53c935f068bf7", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98b8bcde0e4386f127dcfc90cf73b111a4", "guid": "bfdfe7dc352907fc980b868725387e980323b3223ed8a8e2fb7e46287d20f33b"}, {"additionalCompilerOptions": "-fno-objc-arc -fno-objc-arc -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e98b42520d2de3f1b36025ad2b6c093c11f", "guid": "bfdfe7dc352907fc980b868725387e989dec15a37c6480000b992daf56614f58"}, {"additionalCompilerOptions": "-fno-objc-arc -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e98f7ec5d7be0e74570d547d47bb3d45bdb", "guid": "bfdfe7dc352907fc980b868725387e986871c58486be707f9cf86d6d2b225104"}, {"additionalCompilerOptions": "-fno-objc-arc -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e98bf8627fc622d589de7af99714b16627d", "guid": "bfdfe7dc352907fc980b868725387e987ac15191392be1480705791af3761ceb"}], "guid": "bfdfe7dc352907fc980b868725387e986244b9aa7b7595103c06171d6cdac0f8", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e986a42ad16868deef2bc647140f437ecdd", "guid": "bfdfe7dc352907fc980b868725387e9894bfe01c32b7b3ceee0eaf658cd7ceb2"}], "guid": "bfdfe7dc352907fc980b868725387e98dbf7a4f72de9be0bab3a9ea5fb5bd0f8", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98fe063acb0b8fab2fdb50b62dbc14be80", "targetReference": "bfdfe7dc352907fc980b868725387e98c9e4d77647dbd2f60d4df5fb297112b6"}], "guid": "bfdfe7dc352907fc980b868725387e98ffb1655e2965dc892f8fdb6c94f02cdb", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98c9e4d77647dbd2f60d4df5fb297112b6", "name": "nanopb-nanopb_Privacy"}], "guid": "bfdfe7dc352907fc980b868725387e980062393f91a1d2d94e3e5ed3a5aa5da9", "name": "nanopb", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98edeb236a6bea2a184984d344e4936f7f", "name": "nanopb.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}