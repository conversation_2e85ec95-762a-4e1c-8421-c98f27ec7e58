{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e981d77ce01b796835e78c0bb0c4a877ce7", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGNING_ALLOWED": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseInstallations", "PRODUCT_NAME": "FirebaseInstallations", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e986f8a40ba8fafd3a3c337d44aebec0a35", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e982287609c2a6418a9636a3e58aac224cb", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGNING_ALLOWED": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations.modulemap", "PRODUCT_MODULE_NAME": "FirebaseInstallations", "PRODUCT_NAME": "FirebaseInstallations", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98f07aa8a2c9d5f6169d0a9f0937a395e8", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e982287609c2a6418a9636a3e58aac224cb", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGNING_ALLOWED": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations.modulemap", "PRODUCT_MODULE_NAME": "FirebaseInstallations", "PRODUCT_NAME": "FirebaseInstallations", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9830b0ec69a4c060d9118358d4e29e9857", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e988683b9f8228b13ac1ad9ebd468a0cd51", "guid": "bfdfe7dc352907fc980b868725387e9809888d9b0d1eaf3713f4bba972e34ec7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a2e53f98ec81ced8b706576043e719f5", "guid": "bfdfe7dc352907fc980b868725387e98e55c5a35ea6ba239a3226b6eb95e98e6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e4c9152319fa54c106b273b55f76289e", "guid": "bfdfe7dc352907fc980b868725387e9875966f34e5ca2abe2109736b7f019c13"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980f0afae225386f204cca6bc730211ef8", "guid": "bfdfe7dc352907fc980b868725387e98b1b2e8ded13b9efe2e22d59967277ca3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c0d96c9027bd42953deb8470e197c9b6", "guid": "bfdfe7dc352907fc980b868725387e98fdc97946862c11b20c98d466e0e45a84"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98693e750d2dceb8adb567cbb4da804397", "guid": "bfdfe7dc352907fc980b868725387e98902c7e951be6e3326aee30b52664d49e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9895f814f265277eb5bc14c42480c77bd0", "guid": "bfdfe7dc352907fc980b868725387e98858d5e04434ba8bdc0fc54868384124e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987f4e5e6f30b42b48b24152350572db94", "guid": "bfdfe7dc352907fc980b868725387e98f032d87cc1757b0093d902dac22138c3", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98af0c457999b5dfca9a7f7be2b8202315", "guid": "bfdfe7dc352907fc980b868725387e9890b95f76f6d49c5d9fa6a76026404dd9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9882250bd94349012238dbf94e6c274d61", "guid": "bfdfe7dc352907fc980b868725387e98e66d0b59461d6a279194645cdf1529d1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985503d8715cde4892c9e493a9d2b76c9a", "guid": "bfdfe7dc352907fc980b868725387e98e4948af9c4f2d9b385b40e0993d16b50", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985fd9c59fe4d1df4e0430ed4257f4f4b7", "guid": "bfdfe7dc352907fc980b868725387e98c5cdb8a42236bf32c77c5db72ca42c68"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bd0eade3b82d6670efc59f72ccac1922", "guid": "bfdfe7dc352907fc980b868725387e9831e98376e8892ea2d566ddef52b34727", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9813a66b950ecaa3df903d6ebf040c5386", "guid": "bfdfe7dc352907fc980b868725387e986e52932137282bc371f01b846d3b2609"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f87b79f827c94f28a31122ca9a61401b", "guid": "bfdfe7dc352907fc980b868725387e98aaa33ab15abb1a835370d7a5f627d261"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984e48f63d0abcaa4234c5214cf8a7fd38", "guid": "bfdfe7dc352907fc980b868725387e98105aa10dde89662882bcd2fd6c7d7996", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e8b579da8d2a66445c6fbec12db3a675", "guid": "bfdfe7dc352907fc980b868725387e989355883930d5e90652c5aa484bbdbfed"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983aaf6dd20fa3a4840b07a02d15c963c1", "guid": "bfdfe7dc352907fc980b868725387e9879d680e210e93e65bb42d3cfe8ddc975"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fe4230ae646ac6036d8b688a035817da", "guid": "bfdfe7dc352907fc980b868725387e9877aa6439e812dd9c150c683efabe538d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985917750666a4d25f52e1cb35a153b2b8", "guid": "bfdfe7dc352907fc980b868725387e98cb70d78cebf913acac229c916ad07a4b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bdc4dec8fe69c2079ee0cdb4ff1cc7fe", "guid": "bfdfe7dc352907fc980b868725387e981f6900c692f51997485ef75f70d6c1aa"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cd621f54d93efd821f0151b853774084", "guid": "bfdfe7dc352907fc980b868725387e98abe2d417ccfba87db43f11bc655d221f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cc1935266f3fa0f1d29197cd030fe4e7", "guid": "bfdfe7dc352907fc980b868725387e984f7885124a78cb78ab20410dc8384bfe"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986201cba4d245247919b8b972ba1886a4", "guid": "bfdfe7dc352907fc980b868725387e98ad865a62fa5c3f7d603305fce3774d7b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9878de60fa9b4219bf3135a09bace2edec", "guid": "bfdfe7dc352907fc980b868725387e98ba5079f597dbf7dcb3da5b73590eaf96"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9874fe81b91abb7ffdee0e1b81f2509a87", "guid": "bfdfe7dc352907fc980b868725387e98066450e43ac30cb824b448f277da4b27"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985da23ce4a4d55ab1291cf4b52467c0bc", "guid": "bfdfe7dc352907fc980b868725387e985057ba55f286a30a9dc795a36ed767d0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9806405b8ec45ea4c07a6b8bbb859bc010", "guid": "bfdfe7dc352907fc980b868725387e98a3cf4802bd9d528ad44eff3287645e24"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986ab53d0e4cb57d92c74b127f3bac79b3", "guid": "bfdfe7dc352907fc980b868725387e984375bb61245c258c4f0bc245d9717eb9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a1b0f80a643ee70ee7140e68a8b90e97", "guid": "bfdfe7dc352907fc980b868725387e9818705961eb60ce3e6ccdc998168444c2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98289fae4757ba3d0cd311712deb895373", "guid": "bfdfe7dc352907fc980b868725387e98c137f896f4166df95c9c5144ab447413"}], "guid": "bfdfe7dc352907fc980b868725387e984367749ad697fff3758caf4c89852d96", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9819e777de0f27057e5c87903fe2afef71", "guid": "bfdfe7dc352907fc980b868725387e982394d5f43f58da8cc42c195b51601554"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e45009881b93457b7af5d1321ce08576", "guid": "bfdfe7dc352907fc980b868725387e989e939ea1e58ab7ebad940037d6690c98"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d2bcf5a7939de8c897037c1a22b0173e", "guid": "bfdfe7dc352907fc980b868725387e982513809359de2efde56386f7e1e7aa8d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dcfc3eec27a4ce0bd2155b1d327e80fd", "guid": "bfdfe7dc352907fc980b868725387e9845d232e7e0cca40ba4e24c8f44e84220"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987de5431142b1d5b8c4cf1b14067bbf0d", "guid": "bfdfe7dc352907fc980b868725387e981c1278dffdea1c47a55a477c95009425"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982104851ff637dbab34c9b261b2375bf3", "guid": "bfdfe7dc352907fc980b868725387e98b4f6f80e3f9d13759eb2ece38591a556"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9816979394d7666dea83965bce73c283e9", "guid": "bfdfe7dc352907fc980b868725387e985228ee85fca7cc6d7ce2702e73172bb2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b9812d7a79e75870f0043bea64ee6ce0", "guid": "bfdfe7dc352907fc980b868725387e98c08ea3924ac85cf1ddb95cf395c78c6a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980b7d17399f275cc275c697b12b639274", "guid": "bfdfe7dc352907fc980b868725387e988164ed99928c9720a37afc837b418c86"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9885e849bc4cea4c70ef546fe53ff32709", "guid": "bfdfe7dc352907fc980b868725387e98961812f5916e28b096dfcedcc102bf79"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985f3b756443997e55a9958bcf9ef3a0c9", "guid": "bfdfe7dc352907fc980b868725387e98ca89589b981e5877890e336111633a4b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b1f7cf445093c6ddd759afeb6e7a8189", "guid": "bfdfe7dc352907fc980b868725387e985c06d4a623ec1428d69d7c4f54193bec"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986293542a204c5bd9897ec8a2a6aa93f5", "guid": "bfdfe7dc352907fc980b868725387e981044da08cd68b48ed4ed616378cb0644"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987985ef063d4e0813601d6281bb31dfa4", "guid": "bfdfe7dc352907fc980b868725387e980e2026512a60acee1d63ccbfb0c86d9a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981911ca8201147a2101d4acfc1f8be761", "guid": "bfdfe7dc352907fc980b868725387e984add78e6a7c2584a33fc8f89834855f2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ba4a06b4c9a04a89e0523a3326205481", "guid": "bfdfe7dc352907fc980b868725387e98420eea30a9d7856d2b2a96d4d4308daf"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98da38aaba6494310eeb9a272667c8f662", "guid": "bfdfe7dc352907fc980b868725387e989407142a7bcb6f13e8ca18910abd3b00"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98eed6c939ed6c513127ee66922dcfdfd8", "guid": "bfdfe7dc352907fc980b868725387e98a5c2813aea0ac9573c903e0b37ad2e5d"}], "guid": "bfdfe7dc352907fc980b868725387e98a6bb26137e8b0bba1765794360e9eb0b", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e986a42ad16868deef2bc647140f437ecdd", "guid": "bfdfe7dc352907fc980b868725387e98bfdc83b1268d8aa37efb2282e3395677"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98893e4f43ea38aaa1079f62389548f89e", "guid": "bfdfe7dc352907fc980b868725387e9820c9a3e9b13c2fecc0b44f957b4ca2e6"}], "guid": "bfdfe7dc352907fc980b868725387e9810e853eec5f77146d7a3aafebba16953", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e9809313c4e1e5515a4f0e79e0d21765531", "targetReference": "bfdfe7dc352907fc980b868725387e984535f130e81fa6507008242e4e8916fc"}], "guid": "bfdfe7dc352907fc980b868725387e9870a5688d465ac59acc879461d6d5da33", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98a408a4c1f668e62161cdeba76f57d50c", "name": "FirebaseCore"}, {"guid": "bfdfe7dc352907fc980b868725387e984535f130e81fa6507008242e4e8916fc", "name": "FirebaseInstallations-FirebaseInstallations_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities"}, {"guid": "bfdfe7dc352907fc980b868725387e98f10882e1684b8a3dfdec597bc0a47af3", "name": "PromisesObjC"}], "guid": "bfdfe7dc352907fc980b868725387e98566ec9a1d71c4629f4f85ecb735ce614", "name": "FirebaseInstallations", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e9860819b8e327bf41b291e92315614a812", "name": "FirebaseInstallations.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}