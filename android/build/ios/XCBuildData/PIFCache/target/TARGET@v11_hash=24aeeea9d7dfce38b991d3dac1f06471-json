{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e989e347ee79bd39a6c4756867db80824e6", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGNING_ALLOWED": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/Developer/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/Developer/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/google_mobile_ads/google_mobile_ads-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/google_mobile_ads/google_mobile_ads-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/google_mobile_ads/google_mobile_ads.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "google_mobile_ads", "PRODUCT_NAME": "google_mobile_ads", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98f60f21207bdf48370aadaa7a5944056e", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98b85709f544980d18206a69151bc062bb", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGNING_ALLOWED": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/Developer/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/Developer/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/google_mobile_ads/google_mobile_ads-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/google_mobile_ads/google_mobile_ads-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/google_mobile_ads/google_mobile_ads.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "google_mobile_ads", "PRODUCT_NAME": "google_mobile_ads", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98ca83b02fd91b34a498c4c1d10d117548", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98b85709f544980d18206a69151bc062bb", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGNING_ALLOWED": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/Developer/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/Developer/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/google_mobile_ads/google_mobile_ads-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/google_mobile_ads/google_mobile_ads-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/google_mobile_ads/google_mobile_ads.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "google_mobile_ads", "PRODUCT_NAME": "google_mobile_ads", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98d212063812730634d67c1838935e42c2", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9837c80a64f8cd6e1dfd08824f5ed8b7bd", "guid": "bfdfe7dc352907fc980b868725387e982164fc4c60916d0407ff4f2b5aa4327f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982d089b1b7e47b7680d0c7e92f3ecbfca", "guid": "bfdfe7dc352907fc980b868725387e988c0d30af009d0622f0a4925f959cfda8", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98befd02a999f6acccfcccbcfd3c9f1bb0", "guid": "bfdfe7dc352907fc980b868725387e9899c1cd6c727afbe62cd1443798fba7c8", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9843503e4f7977ceab9a41b32cbd7a54e5", "guid": "bfdfe7dc352907fc980b868725387e98338ac8ae3dadcfeb3a3fb51f0096a543", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b3c8dedc7a9871758a62c1f4f6eb14c6", "guid": "bfdfe7dc352907fc980b868725387e983155c1befa51578a07a52a0a25599956", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9801b41adcdcf607854f6e7b6c9bed1845", "guid": "bfdfe7dc352907fc980b868725387e98642b54151b51513cd1a2dd64dcafd6c0", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98135f2f6d34afdffb7531da26e21b0d3a", "guid": "bfdfe7dc352907fc980b868725387e983ee7e82f2d4c72762ea0d14016d7c368", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987d432ed5812235aeb4693103dfbd33e9", "guid": "bfdfe7dc352907fc980b868725387e9801cb5b248c9084579cc8e60eb494b60f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98114912b4798ba11b13f0985a66846500", "guid": "bfdfe7dc352907fc980b868725387e981fe991172e4593b488c4b429a0d8225e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bebee1dbc9d56abc04de618501426436", "guid": "bfdfe7dc352907fc980b868725387e98c81e0815d1581a43cc1af0fad5ce8d05", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981469f4bd2882a2a0f3fd3b18b28f5246", "guid": "bfdfe7dc352907fc980b868725387e98dc8420dc22bf44a06672387aa5224a98", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ed43f4fbc9783efcf8da7a7845e62ccd", "guid": "bfdfe7dc352907fc980b868725387e9827060b0a1fdef85f04bb484e658b7383", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985bb335adc7728cdf2d04ee48ef9d5921", "guid": "bfdfe7dc352907fc980b868725387e98ed1c158cd93ce9bd5aa118e2cd283b4f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981fd0ef00bdd05b4e31702ca97f27b87b", "guid": "bfdfe7dc352907fc980b868725387e98e99c5e414436323606079f3a3fa0ea9f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9867d3822910cd58a15be14c95023b00fe", "guid": "bfdfe7dc352907fc980b868725387e9849f9fb436a0697ecd59328018dd8ae93", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f9cf763ad95df24c1dbfe3f4f1dfde1a", "guid": "bfdfe7dc352907fc980b868725387e98fe4a798a98791898faabe0f22d71259a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9830190ddf05da553fbef954cb433c1c45", "guid": "bfdfe7dc352907fc980b868725387e98141f268b1584124e62e04e74b1b75e88", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b46cc657da6daa8fc56f97ffdb7b519c", "guid": "bfdfe7dc352907fc980b868725387e98301a0a7e58b10af17aceb2504b3a3971", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989534363b78ae7c30172d12b50ad3748f", "guid": "bfdfe7dc352907fc980b868725387e9847bc53c4efa9dfda1c7403af4349bc7f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d72d678464555dd6920788b896ddf463", "guid": "bfdfe7dc352907fc980b868725387e9880b8bc952dd7c79faba8d04e45ea8f13", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987b70c006f404bc0805edb9e60ed53410", "guid": "bfdfe7dc352907fc980b868725387e98e729c3a805d1847502bb5c73bfecb303", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98053e90acc4151d0d92954455eabe9ef3", "guid": "bfdfe7dc352907fc980b868725387e9808bf92fbfa2674bda950d0e2bcb71dd5", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d47d5ae8e4d910139cf140245f610b90", "guid": "bfdfe7dc352907fc980b868725387e98319c4e20f59cbade17b334db40c15ab7", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985c1c5d24232c4eebee66fe54a6683995", "guid": "bfdfe7dc352907fc980b868725387e98a13e606cbbfd39a170e763f81b8c82b0", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e985da9e827eef44a73ab95aff9462753c7", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9879fbbde193a96c071b9d41a9fd29f693", "guid": "bfdfe7dc352907fc980b868725387e9880ce5506b25dd20b711b03704288c587"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98141ca2b5ac95708441eec635667f0cb4", "guid": "bfdfe7dc352907fc980b868725387e98a7557dc539ec4df39cd248dddefe289e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981fbea663d5349a1421dc7e380c2a3e0e", "guid": "bfdfe7dc352907fc980b868725387e98bc3c1b20497e20530aa48f475521f59a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d753c9c4a6f0665a0cf4dda4773b4742", "guid": "bfdfe7dc352907fc980b868725387e98c0da8fcff858b56bb9b4aab652636600"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ca9c9939f75acc5472644e9a329571cd", "guid": "bfdfe7dc352907fc980b868725387e98b1c54618187d63a60e8ed5483155f597"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b08f45da32d9aef80a9f16f74b1f30c7", "guid": "bfdfe7dc352907fc980b868725387e98f0097c4a617414a24a43ccaee2061a65"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981adb0a28bd546a7338b808c023d0bc17", "guid": "bfdfe7dc352907fc980b868725387e989a4fbbb040177b251d713e8075c7d4ad"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9876227a71f0ab5241598296a95c019d0e", "guid": "bfdfe7dc352907fc980b868725387e98005d36b45f215eb302ad727713a9c9be"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98874d671f8c59e1613ff077956dcc63ce", "guid": "bfdfe7dc352907fc980b868725387e9823392b89a2aad567b59d013033d15f1b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98418017c31ade29c532b15c01315a498d", "guid": "bfdfe7dc352907fc980b868725387e981228a83d2b11affd288ecac33aff7d94"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9885b065886bc758ff90ed440b3274649f", "guid": "bfdfe7dc352907fc980b868725387e9854a2591537edaba482cd6403feba19d2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a3f71631893b01aa2c9c46b61389651d", "guid": "bfdfe7dc352907fc980b868725387e9885003a044e613a922b7fa982ebd40e7e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9804230825be3b36f9a91dcecec8be3391", "guid": "bfdfe7dc352907fc980b868725387e9882cfc99a050cfaa4ed27ce805996e941"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9821d3f3a75cce276cf96c6a5a31217ebd", "guid": "bfdfe7dc352907fc980b868725387e98bee2144c4a5bfefd8cb512b8d5baac86"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987173d954715fb5444b3e46a35658c71c", "guid": "bfdfe7dc352907fc980b868725387e98419a9f8a3b82d01236184c3dd702c44c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9865d1f625b7dda33b60232f56af7c0292", "guid": "bfdfe7dc352907fc980b868725387e9899efa4c5119022bafabda491fa07b490"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b5fb775a55aa7d5c772fb1efa107ec14", "guid": "bfdfe7dc352907fc980b868725387e9873ecf4092282782f1466f987e59abdc1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985cd7024aef4664d757c4f3705a1720ae", "guid": "bfdfe7dc352907fc980b868725387e987012ba40086472520aa7e812d3fe9583"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98abc704960623172546c1d8a6a075e91f", "guid": "bfdfe7dc352907fc980b868725387e986ed95c95e4015c310de490d6af006edd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d41b420af9b56656513b903a50d82335", "guid": "bfdfe7dc352907fc980b868725387e98bb82c8b4967432d98dc28c4fd639c956"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981fceeabd5158f02e02baa0aaac4ed711", "guid": "bfdfe7dc352907fc980b868725387e98de00129e3270ecf6ff3b9a1449b10c5c"}], "guid": "bfdfe7dc352907fc980b868725387e98ef387155c7be58dd1abcd21a411a4e6e", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e986a42ad16868deef2bc647140f437ecdd", "guid": "bfdfe7dc352907fc980b868725387e98c961a969fb41a7bdab90c538b30528fd"}], "guid": "bfdfe7dc352907fc980b868725387e9817c34e700eb0a17b1be9eca81b57986b", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e984da9032ff76790acb4b37e742746ef15", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e98cd53d937e824fad9f4527eeeb684cb40", "name": "Google-Mobile-Ads-SDK"}, {"guid": "bfdfe7dc352907fc980b868725387e9804037656a8578d8e730f9d99c54e40e2", "name": "google_mobile_ads-google_mobile_ads"}, {"guid": "bfdfe7dc352907fc980b868725387e988efdc4dd0ac29b43123295eca853f4ed", "name": "webview_flutter_wkwebview"}], "guid": "bfdfe7dc352907fc980b868725387e9811f9347c979613c5502173cd5b43060d", "name": "google_mobile_ads", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98b1f16b2926bf8e21151b2cb149aa6540", "name": "google_mobile_ads.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}