{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98a22c5ccbf0285c9d4d029bfcf737f5c1", "buildSettings": {"CODE_SIGNING_ALLOWED": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/PromisesObjC/PromisesObjC-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/PromisesObjC/PromisesObjC.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FBLPromises", "PRODUCT_NAME": "FBLPromises", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98f8304f447535cf0511d327d681bde568", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9860b386ca9a6c361bcce2b17825120267", "buildSettings": {"CODE_SIGNING_ALLOWED": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/PromisesObjC/PromisesObjC-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/PromisesObjC/PromisesObjC.modulemap", "PRODUCT_MODULE_NAME": "FBLPromises", "PRODUCT_NAME": "FBLPromises", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e984aeea0022e32783c3872a79b073dbbc1", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9860b386ca9a6c361bcce2b17825120267", "buildSettings": {"CODE_SIGNING_ALLOWED": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/PromisesObjC/PromisesObjC-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/PromisesObjC/PromisesObjC.modulemap", "PRODUCT_MODULE_NAME": "FBLPromises", "PRODUCT_NAME": "FBLPromises", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98e2f53314c912cb605cb94fd0cf1db82f", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e983adc6e23a5ba748b9c943b5d3a9f136b", "guid": "bfdfe7dc352907fc980b868725387e980247dfb2315d7ce49243de448503965c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b8ee57d5cff36a634e63fdf6e4bd5a05", "guid": "bfdfe7dc352907fc980b868725387e985fdbb8cfb5eba4a3db67d8e9b552c99d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980ab49806a3b16e096e492fce87887cd5", "guid": "bfdfe7dc352907fc980b868725387e98b48268b1f529135ba5ee63db6ddfd0ad", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ce5800d759d1931909018c161ce3a926", "guid": "bfdfe7dc352907fc980b868725387e98fd29a7ad532bfe248cf2800af2d464ff", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e1b761ad06a32437f9b8bda59d5b9443", "guid": "bfdfe7dc352907fc980b868725387e98a173b562fba62640bf0a8febf4549c48", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a864a21fb004301ae6350d29ad79e24c", "guid": "bfdfe7dc352907fc980b868725387e98582faabdf20fdb57f5503de2363098a9", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d7b14ddcdb5b67a5ee22061745f3b004", "guid": "bfdfe7dc352907fc980b868725387e98a35dad0e48019c6cf533262a27a77b2c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9801f96cf36663ff878d8bd0193a5b579e", "guid": "bfdfe7dc352907fc980b868725387e98b3443c8ca5765b213abd75ffe34b7a48", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b725f9d76fd8b8f43fdf7a56a3fbec28", "guid": "bfdfe7dc352907fc980b868725387e981d22334e39fe6367effef949e6c857da", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98811193f95bac02037035314a4bdfeab7", "guid": "bfdfe7dc352907fc980b868725387e983a2d1bd1b21156468db2ec5203ca3173", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984c155709aaad8571d9ab69795e72d987", "guid": "bfdfe7dc352907fc980b868725387e986e84e7e7e2a95a74d7cac43eb2326438", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98694b3493dab7aab0f789dfaaf10a36b8", "guid": "bfdfe7dc352907fc980b868725387e98ca027684f447f473fff6dd99583914af", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98963c9e7cafcc352b2f76a777ea35e4c0", "guid": "bfdfe7dc352907fc980b868725387e98df666f764dc87877f2a311e669232172", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98eb02e8e3cb39ab4f63a3c8a7868e1408", "guid": "bfdfe7dc352907fc980b868725387e987df9d149997d509a0f0737f0753a8274", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987448f0c33f60703feefa8d99d45448e4", "guid": "bfdfe7dc352907fc980b868725387e980233841344f7ce2a08df176d01444df9", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987f776e448fbea1c8addcd871103c4f14", "guid": "bfdfe7dc352907fc980b868725387e9880dcd8b97f654519b00ee1445b2d48c8", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980cf147a02f8ecba06d13cd7b6fec373f", "guid": "bfdfe7dc352907fc980b868725387e988134d0113d366fb81fa6c98863e075ec", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987e1198c52d7a12e1c32bd51574bf4bcb", "guid": "bfdfe7dc352907fc980b868725387e9893166358aa22d8ca033ba48728c2f39b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984be90e26dc8865dcc3ea5f9092aeb058", "guid": "bfdfe7dc352907fc980b868725387e9857371a90a1bfd85f8c13630618223468", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9885f24ca394d0041109a70e4b18e26457", "guid": "bfdfe7dc352907fc980b868725387e982338bc8c66e6b8481eb6c5e5484539fa", "headerVisibility": "private"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9894c8f5b201917959d4f59fd1cdb8ea31", "guid": "bfdfe7dc352907fc980b868725387e98bf96de95741779415b3f66d058eb0173", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98352d2807168721f787cf27cb372fd5b6", "guid": "bfdfe7dc352907fc980b868725387e983be71e9ba74f330c2b4493d6495f4396", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98cbc0c3cbce8400a41b8cc11ebb5d674f", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9814ae8603b154e8dc1c35523a1aae21f3", "guid": "bfdfe7dc352907fc980b868725387e98b0ccced87c72ec05d579ffe8078b10ae"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984b44dd7aadf9a1e8cea1d62598deecd7", "guid": "bfdfe7dc352907fc980b868725387e988310230468b06e0c9a75126a5d7079ea"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e6a8e6df6b376e4ea8ee33d72e9a19ce", "guid": "bfdfe7dc352907fc980b868725387e98ca34dce9487e372f95bcdce2bf0b3e4b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9894deec6a0df612a31b5ce6449a9d5aaf", "guid": "bfdfe7dc352907fc980b868725387e989a4086102bc3d17b7939f19b6be877b9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98720714de2e0db897302b294ca08ccde4", "guid": "bfdfe7dc352907fc980b868725387e980bdc1b725f7edf6333a18bc5b7d336d7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d3050e366b8a80dd9fb913101510e8ef", "guid": "bfdfe7dc352907fc980b868725387e98156959e2f5e5cb1019986036ec1d384e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9803571f52d202315b2a94b580f84436c5", "guid": "bfdfe7dc352907fc980b868725387e986b0248b7e34a4607592f9f0aa9afb30c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9816d2cb87c350ffd2dfa66dfb105d56eb", "guid": "bfdfe7dc352907fc980b868725387e980dc47270e1180b61fcc02070f14e877f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988b16602ba71d9bf6e6496417cbcf5033", "guid": "bfdfe7dc352907fc980b868725387e9842b117300cb2b05b7369c1717127c8f3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989621ea8106a4a04eb24415c006dc128f", "guid": "bfdfe7dc352907fc980b868725387e9827ebaf2e3109ab6cc5bd817335f9c5da"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d9fb5f15ce987915b5c9df4218944f7a", "guid": "bfdfe7dc352907fc980b868725387e982a404638ad1510ed253ba23f22ff26ae"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988b37e36ae9190ef0c242faa32d53b44e", "guid": "bfdfe7dc352907fc980b868725387e989934edd7fb90c503f2da7e540422f5cb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f5927e170365e3159f7d56aa5ed1f7c2", "guid": "bfdfe7dc352907fc980b868725387e988586ee53a3dd7f19ed7fe30aa4eeb2fc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ecc97f1801a7d475e3fb45c99a179803", "guid": "bfdfe7dc352907fc980b868725387e98e4857d80318137c0de00f3cc19b5db8f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9883be47e86b6cc47dff13d5b31e5c9ad0", "guid": "bfdfe7dc352907fc980b868725387e9823950b0b3536c390a65a5b214ce50859"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d2a95a9cd1cf11c175fca3e73505892d", "guid": "bfdfe7dc352907fc980b868725387e98310759e860d07ff7a4de449c9a33e698"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ad82f48bb94713de3fc05b080ac81c06", "guid": "bfdfe7dc352907fc980b868725387e98b57d40a9aeec67bc01ed0f31103ffe08"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988cd9927b2f5f9a89f4b27edf87cd29b3", "guid": "bfdfe7dc352907fc980b868725387e9828184418b5f0811ca24cd479d4a01fc7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989177ad6eaac9b9d9672f09d2a4b19cf0", "guid": "bfdfe7dc352907fc980b868725387e9817d12163473349a7905939d4fd61e6bf"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985ff31a686a582de196175ea3bd815168", "guid": "bfdfe7dc352907fc980b868725387e984ea8f83666a33dc615c1cc32b6848d82"}], "guid": "bfdfe7dc352907fc980b868725387e988920b01b7e8f0cf1e43db5ebdd30d583", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e986a42ad16868deef2bc647140f437ecdd", "guid": "bfdfe7dc352907fc980b868725387e988f46825c3115b1b1d00c4af8441a97f6"}], "guid": "bfdfe7dc352907fc980b868725387e98a21705b7a3b58446b28a150c0d32706d", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e988c0ca67146b5fde9fe52d123f7c5e863", "targetReference": "bfdfe7dc352907fc980b868725387e98ad53226b339581a6725de188f2c8f823"}], "guid": "bfdfe7dc352907fc980b868725387e984188dd6d26f3d90a4c04dd4e20a77e26", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98ad53226b339581a6725de188f2c8f823", "name": "PromisesObjC-FBLPromises_Privacy"}], "guid": "bfdfe7dc352907fc980b868725387e98f10882e1684b8a3dfdec597bc0a47af3", "name": "PromisesObjC", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e981c795e45f8d875aac88217c6a2a95faa", "name": "FBLPromises.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}