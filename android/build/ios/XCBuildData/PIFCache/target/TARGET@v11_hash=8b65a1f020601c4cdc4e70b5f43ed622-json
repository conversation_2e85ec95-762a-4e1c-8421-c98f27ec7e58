{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9847b6654c4a6fc049a4445bac224f4a05", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGNING_ALLOWED": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/Developer/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/Developer/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/geolocator_apple/geolocator_apple-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/geolocator_apple/geolocator_apple-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/geolocator_apple/geolocator_apple.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "geolocator_apple", "PRODUCT_NAME": "geolocator_apple", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e984936d7cb8feef48513f6131e211caece", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e986b53a6a644977c0881ce0b2e3ff4d8e8", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGNING_ALLOWED": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/Developer/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/Developer/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/geolocator_apple/geolocator_apple-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/geolocator_apple/geolocator_apple-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/geolocator_apple/geolocator_apple.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "geolocator_apple", "PRODUCT_NAME": "geolocator_apple", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98beaaf634ac9c45a28ea8b53ae8cea921", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e986b53a6a644977c0881ce0b2e3ff4d8e8", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGNING_ALLOWED": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/Developer/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/Developer/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/geolocator_apple/geolocator_apple-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/geolocator_apple/geolocator_apple-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/geolocator_apple/geolocator_apple.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "geolocator_apple", "PRODUCT_NAME": "geolocator_apple", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9879f34f1e012c285b844492b857411475", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98d14b73d95ca8e459f7007f31c6383863", "guid": "bfdfe7dc352907fc980b868725387e980ef4dc7532c29cefddc3729f81249df4", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cabe151174705bf4d3f54421b5efbd94", "guid": "bfdfe7dc352907fc980b868725387e989acbdba2b4f57e37a82f82dfbc4b6a88", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b6e96737406a29d847b4cc3b88679bab", "guid": "bfdfe7dc352907fc980b868725387e98ccf33296c77afb7d83454b7cd831b969", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fd6c7f2f49cd41890f74e698a2fc0ecf", "guid": "bfdfe7dc352907fc980b868725387e98c0e1142d20b85845ce4337738948533f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988941ba47086eb406ada17c0862aee756", "guid": "bfdfe7dc352907fc980b868725387e98b4d22474482daf76bebf900c4787a4ea", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9890211958b49521c1e8980c95da1e3d88", "guid": "bfdfe7dc352907fc980b868725387e9861c163610b2f649ece896910dcb5d3c0", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c2f094fa596c62f13e341951efe2cda9", "guid": "bfdfe7dc352907fc980b868725387e98827666d018d0cbfa3a29dffc405c37c2", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e073445b1c3e80eff6ff4b50d9ac6fc1", "guid": "bfdfe7dc352907fc980b868725387e98638ce503f76de7fccf81ce51b3c8367d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f1b4a001df4509e6f8a882151d095fa7", "guid": "bfdfe7dc352907fc980b868725387e98a2bb664801eec5224376b31fb5cb0d4b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9833ff5368e1f87f199434e72fb190e99f", "guid": "bfdfe7dc352907fc980b868725387e98eded081ad84d32301f6cab36b236dbd5", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980ddcfda9df91084df6f770c506811400", "guid": "bfdfe7dc352907fc980b868725387e9879609b44de9cccee41b189e59f22d9ef", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c04608ffb7ed03e4d0e313127072b561", "guid": "bfdfe7dc352907fc980b868725387e98d75cda691380849a16048fe56a59cc35", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9827c6b6409b518bd74f5267a6ed3a22cb", "guid": "bfdfe7dc352907fc980b868725387e981f3fe2209d00e658a00989ce4051a1fb", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98634de7e6f46c4a2db42fc4a5555990c0", "guid": "bfdfe7dc352907fc980b868725387e981a0466bf65d5117c178f7f22ec631484", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980bf2885c17d6128f091317967c52211c", "guid": "bfdfe7dc352907fc980b868725387e985d6cd2f9cd7473d0b0f972a846a20dfd", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988040c36d40b6f7cb038a0169ae25b010", "guid": "bfdfe7dc352907fc980b868725387e98bcacb351ee6587e77fb411198be3d146", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98309eba574350178cc6ce0f75a26ac709", "guid": "bfdfe7dc352907fc980b868725387e98171180797fa255533f9f7994c8560a83", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e986576ae2c21c60200c6d4fa060d500570", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98ea30e5fbf9c510c6644790e495507a6e", "guid": "bfdfe7dc352907fc980b868725387e986e2976ba8451c24dd02a6c6d9dc052a2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985eb04e38c2b4c66531279e5a6239222f", "guid": "bfdfe7dc352907fc980b868725387e98941544c221263f8727c5651833ac7bac"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982c8204122c75cec2f7ee8b73b73092c3", "guid": "bfdfe7dc352907fc980b868725387e982a253298e0f3733c1b9507f412559828"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d518575ef80bccc3ae352893519bd8a1", "guid": "bfdfe7dc352907fc980b868725387e982021aca3f3a1b206818945093d5b26e3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9882dc12a81ba4d5c2ee082c22e4e85d03", "guid": "bfdfe7dc352907fc980b868725387e98d9f834483786fc0c92f7593bcd6dd83c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98eaf3f5a3cb34f28246c4e1e256aed418", "guid": "bfdfe7dc352907fc980b868725387e9824d27b3f537ef9a2045dede749d7c0d4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986a1ad19c5b790cedaf1c5aa3c97b580d", "guid": "bfdfe7dc352907fc980b868725387e980bed89934f29d483fb9437c1ee2c0997"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9859152c6677061bd0fc4ffc968e4b75f1", "guid": "bfdfe7dc352907fc980b868725387e9837785c89cc68442a317aa11bbdf8c941"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983750bf8203b2a1244feededb0533f61c", "guid": "bfdfe7dc352907fc980b868725387e98ae9cfda66bc1e1bfc9abf45be96f0279"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9824ee09df0691cc772034559881a42f44", "guid": "bfdfe7dc352907fc980b868725387e98f58eb8921a9742656ee627060f43a262"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981dce628457c14d7522ed8469a50a9272", "guid": "bfdfe7dc352907fc980b868725387e98d3465a74f12a4301aa2d8c0d61f19e31"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9881e773ae1ff79629cec3fe5efff33e92", "guid": "bfdfe7dc352907fc980b868725387e9871ed1b438edafbb39d8629bebe580cde"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9820ce1d45a2bbbeb58c2b16d1454afef5", "guid": "bfdfe7dc352907fc980b868725387e9800ddb06d077111995f419ba9bca7f614"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985173ea4c393eb4f1d560b66a87f59be5", "guid": "bfdfe7dc352907fc980b868725387e98001cf8cf1b1a8df5390c7082c81acf8d"}], "guid": "bfdfe7dc352907fc980b868725387e989d91e5abbad637a65716b7fef9466ccd", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e986a42ad16868deef2bc647140f437ecdd", "guid": "bfdfe7dc352907fc980b868725387e9847d3b3fdbf8c6c03a84819a79f7ae5aa"}], "guid": "bfdfe7dc352907fc980b868725387e988ec00323e57392a3c283b014e048e33b", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98c4b351c20ac090098b0dcaf5fd3c18e2", "targetReference": "bfdfe7dc352907fc980b868725387e98e1aba8ff8dc833f2269ce0a7182533b3"}], "guid": "bfdfe7dc352907fc980b868725387e98053f546dc39b1661db608be4eb007f0a", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e98e1aba8ff8dc833f2269ce0a7182533b3", "name": "geolocator_apple-geolocator_apple_privacy"}], "guid": "bfdfe7dc352907fc980b868725387e9821d372cc1e7c7587a12aeda843619e39", "name": "geolocator_apple", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e986ff8f87e011522b1b6328c84d9533927", "name": "geolocator_apple.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}