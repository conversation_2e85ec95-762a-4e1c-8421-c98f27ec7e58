{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98d65889532952df0709294a6081addd54", "buildSettings": {"CODE_SIGNING_ALLOWED": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/DKPhotoGallery/DKPhotoGallery-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/DKPhotoGallery/DKPhotoGallery-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/DKPhotoGallery/DKPhotoGallery.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "DKPhotoGallery", "PRODUCT_NAME": "DKPhotoGallery", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9869f20ec37201e248c7624922d1634f7b", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98b528d7b725a8352ff1e55633ebb2695a", "buildSettings": {"CODE_SIGNING_ALLOWED": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/DKPhotoGallery/DKPhotoGallery-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/DKPhotoGallery/DKPhotoGallery-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/DKPhotoGallery/DKPhotoGallery.modulemap", "PRODUCT_MODULE_NAME": "DKPhotoGallery", "PRODUCT_NAME": "DKPhotoGallery", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98c14500e1379a14c1ae1936711f2883c1", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98b528d7b725a8352ff1e55633ebb2695a", "buildSettings": {"CODE_SIGNING_ALLOWED": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/DKPhotoGallery/DKPhotoGallery-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/DKPhotoGallery/DKPhotoGallery-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/DKPhotoGallery/DKPhotoGallery.modulemap", "PRODUCT_MODULE_NAME": "DKPhotoGallery", "PRODUCT_NAME": "DKPhotoGallery", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e986c9bcc06cd6671f77ea0e822839f7725", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e987b7d0970ff91cc968227b81d1710693e", "guid": "bfdfe7dc352907fc980b868725387e98195efb1ee31b334c766f4393b5f25aac", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e985cc650b757c55af98d9abc04b0c2fcc2", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98d2d0685bb3ef2ce6af6c278990a1559f", "guid": "bfdfe7dc352907fc980b868725387e98b52a676c8da57d74b187fa8ded49a46a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983079be2e65f03ea0510d47a3008df1fc", "guid": "bfdfe7dc352907fc980b868725387e98a2dfd2dfe361732be19e6fbe044036c8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f5bc1a07776ac5a0c975d17bd5790a30", "guid": "bfdfe7dc352907fc980b868725387e98ed924485d3838880ecf053b76e232e66"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9860ca615c9d06d31ca6f18d667bfa816e", "guid": "bfdfe7dc352907fc980b868725387e98d968054293d9008021a74ca6f4e291e2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9853c3069be79213e7ab1234a47088dbb4", "guid": "bfdfe7dc352907fc980b868725387e982a49a1a0996d28076284142cadf02d0c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984f9dd5f14089b2df6b08b73a25cc9921", "guid": "bfdfe7dc352907fc980b868725387e98c7f96c19c8ad6edba0d708390ced86d0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986a1ecff269206b9b97aef9060baf2b47", "guid": "bfdfe7dc352907fc980b868725387e9831390412ebaa669a38df5f780a6adfe1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98be284f196a9d543d1dc3992bdf666309", "guid": "bfdfe7dc352907fc980b868725387e9870d5bf64dbf2b24c5eaedb1bd1b0801d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fc70d9a1013e769029a5bf380f89a705", "guid": "bfdfe7dc352907fc980b868725387e984076993fe433593fc0cacf1e31c24939"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9874f7fed31b4908ccbb41233c40d580c3", "guid": "bfdfe7dc352907fc980b868725387e9845d421f8381e8e6d0985fb3fc3fa783b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98277feb8584c7b5f866085f8a2c3084c4", "guid": "bfdfe7dc352907fc980b868725387e98a10c97f44a983688a0ee4e387d491ba9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988e0ef67bbd62c3e5887f07a1a49b774d", "guid": "bfdfe7dc352907fc980b868725387e9821e9cb4b345e3f4cfd4e2bc52060a52a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ff85b1d63548aa26563552ba7f7db23b", "guid": "bfdfe7dc352907fc980b868725387e9840f86160d7673c4d21b1c6a825c8d5a1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9859d4388cf4a8e06602c3105a5cb31917", "guid": "bfdfe7dc352907fc980b868725387e98006222a3bbdea4fb9b82657587c5144b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cb28e6ceb0452532874970f96d03401f", "guid": "bfdfe7dc352907fc980b868725387e98d0084f178f0e970b031c1c5d400f5658"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c1094badab1211ab437e7ed26cfc87fe", "guid": "bfdfe7dc352907fc980b868725387e98fa191db218d155d561cb669a7dc10897"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982b098a4c67c265bdef02a899e7d3b4d0", "guid": "bfdfe7dc352907fc980b868725387e987e8b5104efbed93733b0c3b1ad220484"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9840c423bb81d3689af9ac6581599d65b7", "guid": "bfdfe7dc352907fc980b868725387e98fdd6774a413a08b9195f1960a3171572"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98027b0aaddf44a50720225282947e1079", "guid": "bfdfe7dc352907fc980b868725387e985a320974516cf8a6379052b8887848b5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980af1f034560da099614865475e850838", "guid": "bfdfe7dc352907fc980b868725387e98395e7d7619e100e8c87cb4db1cd0759d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986c32773e5e9ff4e671de8784f6a18eaf", "guid": "bfdfe7dc352907fc980b868725387e982c7185225447c274ce0765b1cd812545"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985a71b4d324d63bcdd7ccfc1b1bec556b", "guid": "bfdfe7dc352907fc980b868725387e981580b51ef8da22ec8d9850c8333d825c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987f44d7d4ce6392eb8729535806e2680f", "guid": "bfdfe7dc352907fc980b868725387e980fcaf493ea137b0e5dda92cbbfb6bfc2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9819018480e9f28f44c146dae53b3e607d", "guid": "bfdfe7dc352907fc980b868725387e98fd12e5361fb0bade0d102f1db0bd2a83"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988e8c3316d7d9abf47ee4270f94d7aede", "guid": "bfdfe7dc352907fc980b868725387e98d0c82d7c568dfd7f293faf19ba850484"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985afe7529195eae2e9c976ab74359eb4f", "guid": "bfdfe7dc352907fc980b868725387e980a17cc9011c2a9ee0c5d62123f0aff4d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9806343f3a8f75530379088c7f42daaee6", "guid": "bfdfe7dc352907fc980b868725387e98a7be32bb99fdbb025916e09f5b1e1668"}], "guid": "bfdfe7dc352907fc980b868725387e985f2c2440c18dc936a7ec9c028935db66", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98214b0af25bff664d2c046fcdebe6db71", "guid": "bfdfe7dc352907fc980b868725387e98e7c564897cac113ddd6da4bddc12af60"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98969d7ce10a55440705d72b4af8566ff1", "guid": "bfdfe7dc352907fc980b868725387e9860c1bab2e50750e32839355b195b1937"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986a42ad16868deef2bc647140f437ecdd", "guid": "bfdfe7dc352907fc980b868725387e9841510fc33ebbc28f2ee704ad1742adf1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b2a7385a5d9265a265c3799d0bdb1f09", "guid": "bfdfe7dc352907fc980b868725387e989e5326feff4d92f0196ab63df33ae068"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a27a22e6958412f527d1f9caa36dc969", "guid": "bfdfe7dc352907fc980b868725387e98c810514734625aa2a2f1dcef3db6af06"}], "guid": "bfdfe7dc352907fc980b868725387e98bb9eadc4b2cf31c7c116a9128c62f083", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98b397a9bae00c4e1d2315230f90e3949b", "targetReference": "bfdfe7dc352907fc980b868725387e98d3f65728b12dd217475d1283ee417937"}], "guid": "bfdfe7dc352907fc980b868725387e98cc5ead80a85542db79015620e9199adf", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98d3f65728b12dd217475d1283ee417937", "name": "DKPhotoGallery-DKPhotoGallery"}, {"guid": "bfdfe7dc352907fc980b868725387e98c46180aea4e87057640961e6db37df0d", "name": "SDWebImage"}, {"guid": "bfdfe7dc352907fc980b868725387e9872eabefc63c14dfe52fb0c95ad90294e", "name": "SwiftyGif"}], "guid": "bfdfe7dc352907fc980b868725387e989d0a1858a86fd6e6731ed20f88a1e515", "name": "DKPhotoGallery", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Swift", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e986e90c628ccd44af657bee5ff4af2f692", "name": "DKPhotoGallery.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}